#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع لدوال دمج الخلفية
"""

import os
import sys
from pathlib import Path

def test_pdf_files():
    """اختبار وجود ملفات PDF"""
    print("🧪 اختبار ملفات PDF...")
    
    # البحث عن ملفات PDF حديثة في مجلد التقارير
    reports_dir = os.path.join(os.path.expanduser('~'), 'Desktop', 'تقارير الامتحانات')
    
    if not os.path.exists(reports_dir):
        print("❌ مجلد التقارير غير موجود")
        return None, None
    
    # البحث عن أحدث ملف تقرير
    pdf_files = []
    for file in os.listdir(reports_dir):
        if file.endswith('.pdf') and 'تقرير_الحضور' in file:
            file_path = os.path.join(reports_dir, file)
            pdf_files.append((file_path, os.path.getmtime(file_path)))
    
    if not pdf_files:
        print("❌ لم يتم العثور على ملفات تقارير")
        return None, None
    
    # ترتيب حسب التاريخ والحصول على الأحدث
    pdf_files.sort(key=lambda x: x[1], reverse=True)
    latest_report = pdf_files[0][0]
    
    print(f"📄 أحدث تقرير: {latest_report}")
    
    # البحث عن ملف خلفية
    background_file = "C:/Users/<USER>/Downloads/5555.pdf"
    if os.path.exists(background_file):
        print(f"🎨 ملف الخلفية: {background_file}")
        return latest_report, background_file
    else:
        print("❌ ملف الخلفية غير موجود")
        return latest_report, None

def test_pdf_validity(pdf_path):
    """اختبار صحة ملف PDF"""
    try:
        from PyPDF2 import PdfReader
        
        print(f"🔍 اختبار صحة الملف: {pdf_path}")
        
        if not os.path.exists(pdf_path):
            print("❌ الملف غير موجود")
            return False
        
        reader = PdfReader(pdf_path)
        pages = len(reader.pages)
        
        print(f"📄 عدد الصفحات: {pages}")
        
        if pages > 0:
            # اختبار قراءة الصفحة الأولى
            first_page = reader.pages[0]
            print(f"📐 أبعاد الصفحة الأولى: {first_page.mediabox}")
            print("✅ الملف صالح")
            return True
        else:
            print("❌ الملف فارغ")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في قراءة الملف: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 60)
    print("🧪 اختبار سريع لملفات PDF والخلفية")
    print("=" * 60)
    
    # البحث عن الملفات
    report_file, background_file = test_pdf_files()
    
    if report_file:
        print(f"\n📄 اختبار ملف التقرير:")
        report_valid = test_pdf_validity(report_file)
        
        if background_file:
            print(f"\n🎨 اختبار ملف الخلفية:")
            background_valid = test_pdf_validity(background_file)
            
            if report_valid and background_valid:
                print("\n✅ جميع الملفات صالحة!")
                print("💡 يمكنك الآن تجربة إنشاء تقرير جديد")
            else:
                print("\n⚠️ بعض الملفات تالفة")
        else:
            print("\n⚠️ ملف الخلفية غير متوفر")
    else:
        print("\n❌ لم يتم العثور على ملفات للاختبار")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
