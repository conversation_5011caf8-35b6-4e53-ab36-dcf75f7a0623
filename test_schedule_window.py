#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نافذة تعليمات جدولة الامتحان
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt

# إضافة مسار المجلد الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sub26_window import ExamScheduleInstructions

def test_schedule_window():
    """اختبار نافذة تعليمات الجدولة"""
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # إنشاء النافذة
    window = ExamScheduleInstructions()
    window.show()
    
    print("تم فتح نافذة تعليمات جدولة الامتحان بنجاح!")
    
    # تشغيل التطبيق
    sys.exit(app.exec_())

if __name__ == "__main__":
    test_schedule_window()
