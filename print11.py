import os
import sys
import sqlite3
import traceback
from datetime import datetime
import subprocess

# ================= إعدادات عامة =================
# الجدول الأول: صفان، 6 أعمدة
COL_WIDTHS_TABLE1 = [20, 25, 25, 25, 70, 30]
# الجدول الثاني: 6 أعمدة - تعديل العناوين حسب الصورة المرفقة
COL_WIDTHS_TABLE2 = [30, 40, 30, 30, 40, 25]
# تعديل عناوين الجدول الثاني
TABLE2_HEADERS = ['الغائبون', 'الاسم والنسب', 'التوقيع', 'الرقم الوطني', 'الاسم الكامل', 'رقم الامتحان']
# الجدول الثالث (أستاذ، تخصص، توقيع) أسفل الصفحة
COL_WIDTHS_TABLE3 = [30, 40, 35]
# الجدول الرابع (رئيس المركز، توقيع) أسفل الصفحة
COL_WIDTHS_TABLE4 = [35, 45]

# إضافة ارتفاعات الصفوف
ROW_HEIGHT_TABLE1 = 8  # ارتفاع صفوف الجدول الأول
ROW_HEIGHT_TABLE2 = 6   # ارتفاع صفوف الجدول الثاني (جدول المترشحين) - سيتم تعديله ديناميكيًا حسب عدد الصفوف
ROW_HEIGHT_HEADER = 10  # ارتفاع صفوف الرأس
ROW_HEIGHT_TABLE_HEADER = 12  # ارتفاع صف رأس الجدول
# تقسيم ارتفاعات الجداول السفلية لكل جدول على حدة
ROW_HEIGHT_TABLE3_HEADER = 10  # ارتفاع رأس جدول الأستاذ
ROW_HEIGHT_TABLE3_DATA = 8    # ارتفاع صفوف بيانات جدول الأستاذ
ROW_HEIGHT_TABLE4_HEADER = 10  # ارتفاع رأس جدول رئيس المركز
ROW_HEIGHT_TABLE4_DATA = 16    # ارتفاع صفوف بيانات جدول رئيس المركز

# إضافة إعدادات للهوامش (بالملليمتر)
PAGE_MARGIN_TOP = 0.2     # الهامش العلوي للصفحة
PAGE_MARGIN_BOTTOM = 0.2  # الهامش السفلي للصفحة
PAGE_MARGIN_LEFT = 10     # الهامش الأيسر للصفحة
PAGE_MARGIN_RIGHT = 10    # الهامش الأيمن للصفحة

PT_TO_MM = 0.3528
LOGO_W_PT, LOGO_H_PT = 200, 80
BOX1_W_PT, BOX2_W_PT, BOX3_W_PT = 320, 80, 150
TITLE_H_PT = 40
LOGO_W = LOGO_W_PT * PT_TO_MM
LOGO_H = LOGO_H_PT * PT_TO_MM
BOX1_W = BOX1_W_PT * PT_TO_MM
BOX2_W = BOX2_W_PT * PT_TO_MM
BOX3_W = BOX3_W_PT * PT_TO_MM
BOX_H = TITLE_H_PT * PT_TO_MM

try:
    from fpdf import FPDF
    import arabic_reshaper
    from bidi.algorithm import get_display
except ImportError:
    subprocess.check_call([sys.executable, "-m", "pip", "install", "fpdf2", "arabic-reshaper", "python-bidi"])
    from fpdf import FPDF
    import arabic_reshaper
    from bidi.algorithm import get_display

class ArabicPDF(FPDF):
    def __init__(self):
        super().__init__('P','mm','A4')
        # تعيين الهوامش
        self.set_margins(PAGE_MARGIN_LEFT, PAGE_MARGIN_TOP, PAGE_MARGIN_RIGHT)
        self.set_auto_page_break(auto=True, margin=PAGE_MARGIN_BOTTOM)
        fonts_dir = os.path.join(os.path.dirname(__file__), 'fonts')
        # تصحيح تحذير Deprecation Warning بإزالة المعامل uni=True
        self.add_font('Arial', '', os.path.join(fonts_dir, 'arial.ttf'))
        self.add_font('Arial', 'B', os.path.join(fonts_dir, 'arialbd.ttf'))
        self.set_font('Arial', '', 12)
        # تعيين سمك الخط الافتراضي
        self.set_line_width(0.4)

    def ar_text(self, txt: str) -> str:
        """
        تحويل النص العربي ليتم عرضه بشكل صحيح
        إذا كان النص يحتوي على رمز \n سيتم تجاهله وإرجاع نص مباشرة
        لأن fpdf تتعامل مع السطور الجديدة بشكل مختلف
        """
        reshaped = arabic_reshaper.reshape(str(txt))
        return get_display(reshaped)

    def multi_line_ar_text(self, txt: str, cell_width: float, font_size: int = 12) -> list:
        """
        تقسيم النص إلى سطور لتتناسب مع عرض الخلية

        المعاملات:
            txt: النص المراد تقسيمه
            cell_width: عرض الخلية بالملليمتر
            font_size: حجم الخط

        العوائد:
            قائمة بأسطر النص بعد التقسيم
        """
        lines = []
        # تقسيم النص إلى كلمات
        words = txt.split(' ')
        current_line = ""

        for word in words:
            # تقدير عرض السطر الحالي مع الكلمة المضافة
            test_line = current_line + " " + word if current_line else word
            # تحويل مؤقت للنص العربي لحساب العرض بشكل صحيح
            ar_test_line = self.ar_text(test_line)

            # حساب عرض النص التقريبي (استخدام تقدير بسيط)
            self.set_font('Arial', '', font_size)
            width = self.get_string_width(ar_test_line)

            # إذا تجاوز العرض المسموح، نضيف السطر الحالي ونبدأ بسطر جديد
            if width > cell_width and current_line:
                lines.append(current_line)
                current_line = word
            else:
                current_line = test_line

        # إضافة السطر الأخير إذا كان غير فارغ
        if current_line:
            lines.append(current_line)

        return lines


def fetch_records(db_path: str):
    conn = sqlite3.connect(db_path)
    cur = conn.cursor()
    # جلب شعار المؤسسة
    cur.execute("SELECT ImagePath1 FROM بيانات_المؤسسة LIMIT 1")
    logo_row = cur.fetchone()
    logo_path = logo_row[0] if logo_row and os.path.exists(logo_row[0]) else None

    # جلب بيانات الامتحانات مع تعديل الترتيب: رقم الامتحان أولاً ثم رقم القاعة رقمياً
    query = '''
    SELECT رقم_الامتحان, الاسم_الكامل, الرمز, مركز_الامتحان, المستوى, القسم, القاعة
    FROM امتحانات
    ORDER BY CAST(رقم_الامتحان AS INTEGER), CAST(القاعة AS INTEGER)
    '''
    cur.execute(query)
    cols = [c[0] for c in cur.description]
    recs = [dict(zip(cols, row)) for row in cur.fetchall()]
    conn.close()
    return logo_path, recs


def generate_report(logo_path, records, output_path, report_title=None, subject_data=None):
    pdf = ArabicPDF()
    margin = 5
    usable_w = pdf.w - 2 * margin

    # إذا لم يتم تحديد عنوان التقرير، نبحث عنه في قاعدة البيانات
    if not report_title:
        try:
            # البحث عن العنوان2 في جدول_الامتحان
            db_path = os.path.join(os.path.dirname(__file__), 'data.db')
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT العنوان2 FROM جدول_الامتحان LIMIT 1")
            result = cursor.fetchone()
            conn.close()

            if result and result[0]:
                report_title = result[0]
        except Exception as e:
            print(f"خطأ في استرجاع عنوان التقرير من قاعدة البيانات: {str(e)}")

    # تجميع حسب القاعة مع الحفاظ على الترتيب حسب رقم الامتحان
    rooms = {}
    for rec in records:
        room = rec.get('القاعة','')
        rooms.setdefault(room, []).append(rec)

    # ترتيب القاعات رقمياً
    sorted_rooms = sorted(rooms.keys(), key=lambda x: int(x) if x.isdigit() else float('inf'))

    # تحديد ارتفاع صفوف جدول المترشحين بناءً على عدد الصفوف
    global ROW_HEIGHT_TABLE2
    max_records_per_room = max([len(rooms[room]) for room in rooms]) if rooms else 0

    # تعديل ارتفاع الصفوف بناءً على عدد المترشحين
    if max_records_per_room <= 20:
        ROW_HEIGHT_TABLE2 = 7  # ارتفاع أكبر للصفوف إذا كان عدد المترشحين 20 أو أقل
    elif 20 < max_records_per_room <= 25:
        ROW_HEIGHT_TABLE2 = 6  # ارتفاع متوسط للصفوف إذا كان عدد المترشحين بين 21 و 25
    elif 25 < max_records_per_room <= 30:
        ROW_HEIGHT_TABLE2 = 5  # ارتفاع أصغر للصفوف إذا كان عدد المترشحين بين 26 و 30
    else:
        ROW_HEIGHT_TABLE2 = 5  # ارتفاع أصغر للصفوف إذا كان عدد المترشحين أكثر من 30

    for room in sorted_rooms:
        recs = rooms[room]
        # ترتيب السجلات داخل كل قاعة حسب رقم الامتحان
        recs.sort(key=lambda x: int(x.get('رقم_الامتحان', '0')))

        pdf.add_page()
        y = pdf.get_y()
        # إضافة الشعار
        if logo_path:
            x_logo = (pdf.w - LOGO_W) / 2
            pdf.image(logo_path, x=x_logo, y=y, w=LOGO_W, h=LOGO_H)
        y += LOGO_H + 5

        # مربعات: المادة، القاعة، العنوان - بارتفاع موحد
        pdf.set_draw_color(0,0,255)
        pdf.set_line_width(0.4)  # تعديل سمك الخط

        # تحديد ارتفاع موحد للمربعات العلوية
        FIXED_BOX_HEIGHT = 11  # ارتفاع موحد للعناوين

        x = margin
        # استخدام عنوان التقرير المخصص إذا كان متاحاً
        title_text = report_title if report_title else 'محضر بيانات الامتحانات'

        # تحديد نص المادة
        subject_text = 'المادة:'
        if subject_data and 'المادة' in subject_data and subject_data['المادة']:
            subject_text = f"المادة: {subject_data['المادة']}"

        # تعيين لون أزرق غامق للخط
        pdf.set_text_color(0, 0, 128)  # أزرق غامق (RGB: 0, 0, 128)

        for w, text, size in [(BOX3_W, subject_text, 14), (BOX2_W, f'{room}', 14), (BOX1_W, title_text, 14)]:
            pdf.set_xy(x, y)

            # استخدام خط Calibri غامق بحجم 14 لجميع الخلايا
            pdf.set_font('Arial','B', 14)  # استخدام Arial كبديل لـ Calibri لأنه متوفر في FPDF

            # تحديد محاذاة النص في الخلية
            align = 'R' if w == BOX3_W else 'C'  # محاذاة يمين لخلية المادة، ومركز للبقية

            # استخدام ارتفاع موحد لجميع الخلايا
            pdf.cell(w, FIXED_BOX_HEIGHT, pdf.ar_text(text), border=1, align=align)

            x += w

        # إعادة لون النص إلى الأسود للعناصر الأخرى
        pdf.set_text_color(0, 0, 0)
        pdf.set_font('Arial','',12)
        y += FIXED_BOX_HEIGHT + 5

        # الإبقاء على سمك الخط 0.5 للجدول الأول
        # الجدول الأول: صفان، 6 أعمدة مقلوبة
        cols1 = COL_WIDTHS_TABLE1

        # استخدام بيانات المادة إذا كانت متوفرة
        date_value = ''  # قيمة فارغة للتاريخ
        time_value = ''  # قيمة فارغة للتوقيت

        if subject_data:
            if 'التاريخ' in subject_data and subject_data['التاريخ']:
                date_value = subject_data['التاريخ']
            if 'التوقيت' in subject_data and subject_data['التوقيت']:
                time_value = subject_data['التوقيت']

        row1 = ['المستوى', recs[0].get('المستوى',''), 'التاريخ', date_value, 'عدد المترشحين', str(len(recs))]
        row2 = ['مركز الامتحان', recs[0].get('مركز_الامتحان',''), 'التوقيت', time_value, 'عدد الغائبين', '']
        pdf.set_font('Arial','B',12)
        pdf.set_fill_color(230,230,230)
        x = margin
        for i, cell in enumerate(reversed(row1)):
            pdf.set_xy(x, y)
            pdf.cell(cols1[i], ROW_HEIGHT_TABLE1, pdf.ar_text(cell), border=1, align='C', fill=True)
            x += cols1[i]
        y += ROW_HEIGHT_TABLE1; x = margin
        for i, cell in enumerate(reversed(row2)):
            pdf.set_xy(x, y)
            pdf.cell(cols1[i], ROW_HEIGHT_TABLE1, pdf.ar_text(cell), border=1, align='C', fill=True)
            x += cols1[i]
        y += 9

        # الجدول الثاني: 6 أعمدة - تحديث ليطابق الصورة المرفقة
        cols2 = COL_WIDTHS_TABLE2
        pdf.set_font('Arial','B',12)
        pdf.set_fill_color(200,200,200)

        # رسم الخلايا من اليمين إلى اليسار بشكل يدوي
        # 1. رسم خلايا رأس الجدول الأساسية (العناوين) من العمود الثالث حتى السادس
        x = margin
        x_right_columns = x + cols2[0] + cols2[1]  # موقع بداية العمود الثالث من اليمين

        # رسم العناوين من العمود الثالث إلى السادس (من اليمين لليسار)
        for i in range(2, len(TABLE2_HEADERS)):
            current_x = x_right_columns + sum(cols2[2:i])
            pdf.set_xy(current_x, y)
            pdf.cell(cols2[i], ROW_HEIGHT_TABLE_HEADER, pdf.ar_text(TABLE2_HEADERS[i]), border=1, align='C', fill=True)

        # 2. تقسيم الخليتين الأولى والثانية من اليمين
        # الصف الأول (مدمج) مع إضافة نص "الغائبون"
        pdf.set_xy(x, y)
        half_height = ROW_HEIGHT_TABLE_HEADER / 2
        pdf.cell(cols2[0] + cols2[1], half_height, pdf.ar_text("الغائبون"), border=1, align='C', fill=True)

        # الصف الثاني (مقسم إلى عمودين)
        # العمود الأول من اليمين
        pdf.set_xy(x, y + half_height)
        pdf.cell(cols2[0], half_height, pdf.ar_text("رقم الامتحان"), border=1, align='C', fill=True)

        # العمود الثاني من اليمين
        pdf.set_xy(x + cols2[0], y + half_height)
        pdf.cell(cols2[1], half_height, pdf.ar_text("الاسم والنسب"), border=1, align='C', fill=True)

        y += ROW_HEIGHT_TABLE_HEADER
        pdf.set_font('Arial','',12)

        # محتوى الجدول - تقليص عدد الصفوف في العمودين الأول والثاني إلى 10 صفوف فقط

        # رسم الصفوف
        for i, rec in enumerate(recs):
            x = margin

            # تعديل البيانات - ترك العمودين الأول والثاني فارغين
            data = ['', '', '', rec.get('الرمز',''), rec.get('الاسم_الكامل',''), rec.get('رقم_الامتحان','')]

            # رسم خلايا الصف
            for j, cell in enumerate(data):
                pdf.set_xy(x, y)

                # إذا كان العمود الأول أو الثاني ووصلنا إلى الصف العاشر، نتوقف عن رسم خلايا هذه الأعمدة
                if (j < 2) and (i >= 10):
                    # نتخطى رسم الخلية للعمود الأول والثاني بعد الصف العاشر
                    pass
                else:
                    pdf.cell(cols2[j], ROW_HEIGHT_TABLE2, pdf.ar_text(cell), border=1, align='C')

                # تحديث الموقع الأفقي للخلية التالية
                x += cols2[j]

            # تحديث الموقع الرأسي للصف التالي
            y += ROW_HEIGHT_TABLE2

            # الانتقال إلى صفحة جديدة عند الحاجة
            if y > pdf.h - 20:
                pdf.add_page(); y = pdf.get_y()

        # إضافة مسافة 5 نقاط بين جدول الحضور والجدولين أسفله
        y += 3

        # الجدولين الإضافيين أسفل الصفحة بشكل غير معكوس (طبيعي)
        # جدول الأستاذ (3 أعمدة بترتيب معكوس)
        pdf.set_font('Arial','B',12)
        pdf.set_fill_color(200,200,200)
        x = margin
        for w, hdr in zip(COL_WIDTHS_TABLE3, ['التوقيع','التخصص','الأستاذ']):
            pdf.set_xy(x, y)
            pdf.cell(w, ROW_HEIGHT_TABLE3_HEADER, pdf.ar_text(hdr), border=1, align='C', fill=True)
            x += w

        # جدول رئيس المركز (2 أعمدة بترتيب معكوس) بفاصل 10 مم
        x += 10
        for w, hdr in zip(COL_WIDTHS_TABLE4, ['التوقيع','رئيس المركز']):
            pdf.set_xy(x, y)
            pdf.cell(w, ROW_HEIGHT_TABLE4_HEADER, pdf.ar_text(hdr), border=1, align='C', fill=True)
            x += w

        y += max(ROW_HEIGHT_TABLE3_HEADER, ROW_HEIGHT_TABLE4_HEADER)
        pdf.set_font('Arial','',12)

        for i in range(2):
            x = margin
            for w in COL_WIDTHS_TABLE3:
                pdf.set_xy(x, y)
                # Usar ROW_HEIGHT_TABLE3_DATA en lugar de ROW_HEIGHT_TABLE3_4
                pdf.cell(w, ROW_HEIGHT_TABLE3_DATA, '', border=1)
                x += w
            if i == 0:
                x += 10
                for w in COL_WIDTHS_TABLE4:
                    pdf.set_xy(x, y)
                    # Usar ROW_HEIGHT_TABLE4_DATA en lugar de ROW_HEIGHT_TABLE3_4
                    pdf.cell(w, ROW_HEIGHT_TABLE4_DATA, '', border=1)
                    x += w
            # Incrementar y según el tipo de dato que estamos procesando
            y += (ROW_HEIGHT_TABLE3_DATA if i == 0 else ROW_HEIGHT_TABLE3_DATA)

    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    pdf.output(output_path)
    print(f"تم إنشاء التقرير: {output_path}")

def print_exams_report(parent=None, level=None, report_title=None, subject_data=None):
    """
    دالة لإنشاء محضر توقيعات المترشحين، يمكن استدعاؤها من واجهات PyQt5

    المعاملات:
        parent: كائن النافذة الأم (لعرض رسائل)
        level: المستوى لتصفية البيانات (اختياري)
        report_title: عنوان المحضر (اختياري)
        subject_data: بيانات المادة (اختياري)

    العوائد:
        (success, output_path, message): ثلاثية تحدد نجاح العملية ومسار الملف ورسالة النتيجة
    """
    try:
        # تحديد مسار قاعدة البيانات
        db_path = os.path.join(os.path.dirname(__file__), 'data.db')

        # تخصيص استعلام SQL حسب المستوى إذا تم تحديده
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # استخدام عنوان التقرير المخصص إذا تم تمريره

        # استعلام مخصص إذا تم تحديد المستوى
        if level:
            query = '''
            SELECT رقم_الامتحان, الاسم_الكامل, الرمز, مركز_الامتحان, المستوى, القسم, القاعة
            FROM امتحانات
            WHERE المستوى = ?
            ORDER BY CAST(رقم_الامتحان AS INTEGER), CAST(القاعة AS INTEGER)
            '''
            cursor.execute(query, (level,))
        else:
            query = '''
            SELECT رقم_الامتحان, الاسم_الكامل, الرمز, مركز_الامتحان, المستوى, القسم, القاعة
            FROM امتحانات
            ORDER BY CAST(رقم_الامتحان AS INTEGER), CAST(القاعة AS INTEGER)
            '''
            cursor.execute(query)

        cols = [c[0] for c in cursor.description]
        records = [dict(zip(cols, row)) for row in cursor.fetchall()]

        # جلب شعار المؤسسة
        cursor.execute("SELECT ImagePath1 FROM بيانات_المؤسسة LIMIT 1")
        logo_row = cursor.fetchone()
        logo_path = logo_row[0] if logo_row and os.path.exists(logo_row[0]) else None

        conn.close()

        # التحقق من وجود سجلات
        if not records:
            return False, None, "لم يتم العثور على سجلات مطابقة."

        # إنشاء مجلد التقارير إذا لم يكن موجوداً
        reports_dir = os.path.join(os.path.expanduser('~'), 'Desktop', 'تقارير الامتحانات')
        if not os.path.exists(reports_dir):
            os.makedirs(reports_dir)

        # تحديد اسم الملف بناءً على المستوى
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        level_suffix = f"_{level}" if level else ""
        output_path = os.path.join(reports_dir, f"تقرير_الحضور{level_suffix}_{timestamp}.pdf")

        # إنشاء التقرير مع تمرير عنوان التقرير وبيانات المادة
        generate_report(logo_path, records, output_path, report_title, subject_data)

        # تطبيق خلفية PDF إذا كانت متاحة
        try:
            from background_applier import apply_background_if_available
            apply_background_if_available(db_path, output_path)
        except ImportError:
            print("ℹ️ وحدة تطبيق الخلفية غير متاحة")
        except Exception as e:
            print(f"⚠️ خطأ في تطبيق الخلفية: {e}")

        # لا نفتح الملف تلقائياً - سيتم فتحه من الواجهة الرئيسية بعد تطبيق الخلفية
        print("ℹ️ تم إنشاء تقرير لوائح الحضور بنجاح - لن يتم فتحه تلقائياً لتطبيق الخلفية أولاً")

        return True, output_path, "تم إنشاء التقرير بنجاح."
    except Exception as e:
        traceback.print_exc()
        return False, None, f"حدث خطأ: {str(e)}"

if __name__=='__main__':
    try:
        db = os.path.join(os.path.dirname(__file__), 'data.db')
        logo, recs = fetch_records(db)
        out = os.path.join(os.path.expanduser('~'),'Desktop','تقارير الامتحانات', f"report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf")
        generate_report(logo, recs, out)
    except Exception as e:
        print(f"خطأ: {e}")
        traceback.print_exc()
