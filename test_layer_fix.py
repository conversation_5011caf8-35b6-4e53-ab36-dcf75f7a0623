#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح مشكلة تراكم الطبقات في PDF
"""

import os
import sys

def test_layer_accumulation():
    """اختبار مشكلة تراكم الطبقات"""
    print("🧪 اختبار إصلاح تراكم الطبقات في PDF...")
    
    try:
        from PyPDF2 import PdfWriter, PdfReader
        
        # البحث عن ملف خلفية للاختبار
        background_file = "C:/Users/<USER>/Downloads/5555.pdf"
        
        if not os.path.exists(background_file):
            print("❌ ملف الخلفية غير موجود للاختبار")
            return
        
        print(f"🎨 ملف الخلفية: {background_file}")
        
        # اختبار قراءة ملف الخلفية عدة مرات
        print("🔄 اختبار قراءة الخلفية عدة مرات...")
        
        for i in range(3):
            try:
                # قراءة جديدة في كل مرة (يحاكي الإصلاح الجديد)
                fresh_reader = PdfReader(background_file)
                fresh_page = fresh_reader.pages[0]
                
                print(f"   📄 قراءة {i+1}: أبعاد الصفحة {fresh_page.mediabox}")
                print(f"   📊 معرف الصفحة: {id(fresh_page)}")  # للتأكد من أنها نسخة جديدة
                
            except Exception as e:
                print(f"   ❌ خطأ في القراءة {i+1}: {e}")
        
        print("✅ تم اختبار قراءة الخلفية بنجاح - كل قراءة منفصلة")
        
        # البحث عن أحدث تقرير للاختبار
        reports_dir = os.path.join(os.path.expanduser('~'), 'Desktop', 'تقارير الامتحانات')
        
        if os.path.exists(reports_dir):
            pdf_files = []
            for file in os.listdir(reports_dir):
                if file.endswith('.pdf') and 'تقرير_الحضور' in file:
                    file_path = os.path.join(reports_dir, file)
                    pdf_files.append((file_path, os.path.getmtime(file_path)))
            
            if pdf_files:
                # أحدث تقرير
                pdf_files.sort(key=lambda x: x[1], reverse=True)
                latest_report = pdf_files[0][0]
                
                print(f"📄 أحدث تقرير: {latest_report}")
                
                # اختبار عدد الصفحات
                try:
                    report_reader = PdfReader(latest_report)
                    page_count = len(report_reader.pages)
                    print(f"📊 عدد صفحات التقرير: {page_count}")
                    
                    if page_count > 1:
                        print(f"✅ التقرير يحتوي على {page_count} صفحة - مناسب لاختبار الإصلاح")
                        print("💡 يمكنك الآن تجربة إنشاء تقرير جديد لاختبار الإصلاح")
                    else:
                        print("ℹ️ التقرير يحتوي على صفحة واحدة فقط")
                        
                except Exception as e:
                    print(f"❌ خطأ في قراءة التقرير: {e}")
            else:
                print("ℹ️ لا توجد تقارير للاختبار")
        else:
            print("ℹ️ مجلد التقارير غير موجود")
            
    except ImportError:
        print("❌ مكتبة PyPDF2 غير متوفرة")
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🔧 اختبار إصلاح مشكلة تراكم الطبقات")
    print("=" * 60)
    
    print("📋 تفسير المشكلة:")
    print("   - كانت الصفحة الأولى ممتازة")
    print("   - الصفحة الثانية تحتوي على طبقة إضافية من الصفحة الأولى")
    print("   - الصفحة الثالثة تحتوي على طبقات من الصفحة الأولى والثانية")
    print("   - وهكذا...")
    
    print("\n🔧 الحل المُطبق:")
    print("   - إنشاء نسخة جديدة من الخلفية لكل صفحة")
    print("   - تجنب إعادة استخدام نفس كائن صفحة الخلفية")
    print("   - استخدام fresh_background_reader لكل صفحة")
    
    print("\n" + "-" * 60)
    
    test_layer_accumulation()
    
    print("\n" + "=" * 60)
    print("✅ الإصلاح تم تطبيقه! جرب إنشاء تقرير جديد الآن.")
    print("=" * 60)

if __name__ == "__main__":
    main()
