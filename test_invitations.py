#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تطبيق الحل المحسن على استدعاءات المترشحين
"""

import os
import sys

def test_invitations_setup():
    """اختبار إعداد استدعاءات المترشحين"""
    print("🧪 اختبار إعداد استدعاءات المترشحين...")
    
    # التحقق من وجود ملفات البرنامج الأساسية
    required_files = [
        'sub40_window.py',
        'print12.py',
        'data.db'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ ملفات مفقودة: {', '.join(missing_files)}")
        return False
    
    print("✅ جميع الملفات المطلوبة موجودة")
    
    # اختبار استيراد الوحدات
    try:
        import print12
        print("✅ تم استيراد print12 بنجاح")
        
        # التحقق من وجود الدالة المطلوبة
        if hasattr(print12, 'print_exams_report'):
            print("✅ دالة print_exams_report موجودة في print12")
        else:
            print("❌ دالة print_exams_report غير موجودة في print12")
            return False
            
    except ImportError as e:
        print(f"❌ فشل في استيراد print12: {e}")
        return False
    
    # التحقق من قاعدة البيانات والبيانات المطلوبة للاستدعاءات
    try:
        import sqlite3
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # التحقق من وجود جدول امتحانات
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='امتحانات'")
        if cursor.fetchone():
            print("✅ جدول امتحانات موجود")
            
            # عد السجلات والمؤسسات
            cursor.execute("SELECT COUNT(*) FROM امتحانات")
            total_candidates = cursor.fetchone()[0]
            
            print(f"📊 عدد سجلات المترشحين: {total_candidates}")
            
            if total_candidates > 0:
                print("✅ توجد بيانات للمترشحين")
                
                # فحص المؤسسات الأصلية للاستدعاءات المصنفة
                try:
                    cursor.execute("SELECT COUNT(DISTINCT \"المؤسسة_الأصلية\") FROM امتحانات WHERE \"المؤسسة_الأصلية\" IS NOT NULL AND \"المؤسسة_الأصلية\" != ''")
                    institution_count = cursor.fetchone()[0]
                    print(f"📊 عدد المؤسسات الأصلية: {institution_count}")
                    
                    if institution_count > 0:
                        # عرض عينة من المؤسسات
                        cursor.execute("SELECT \"المؤسسة_الأصلية\", COUNT(*) FROM امتحانات WHERE \"المؤسسة_الأصلية\" IS NOT NULL AND \"المؤسسة_الأصلية\" != '' GROUP BY \"المؤسسة_الأصلية\" LIMIT 3")
                        institutions = cursor.fetchall()
                        
                        print("📋 عينة من المؤسسات المتوفرة:")
                        for institution, count in institutions:
                            print(f"   • {institution}: {count} مترشح")
                        
                        print("✅ يمكن إنشاء استدعاءات مصنفة حسب المؤسسة")
                    else:
                        print("⚠️ لا توجد مؤسسات أصلية - فقط استدعاءات شاملة")
                except:
                    print("⚠️ عمود المؤسسة_الأصلية غير متوفر")
                
                # فحص البيانات الأساسية للاستدعاءات
                cursor.execute("SELECT COUNT(*) FROM امتحانات WHERE الاسم_الكامل IS NOT NULL AND الاسم_الكامل != ''")
                names_count = cursor.fetchone()[0]
                print(f"📊 عدد المترشحين بأسماء كاملة: {names_count}")
                
                cursor.execute("SELECT COUNT(*) FROM امتحانات WHERE رقم_الامتحان IS NOT NULL AND رقم_الامتحان != ''")
                numbers_count = cursor.fetchone()[0]
                print(f"📊 عدد المترشحين بأرقام امتحان: {numbers_count}")
                
                if names_count > 0 and numbers_count > 0:
                    print("✅ البيانات الأساسية متوفرة لإنشاء الاستدعاءات")
                else:
                    print("⚠️ بعض البيانات الأساسية مفقودة")
            else:
                print("❌ لا توجد بيانات للمترشحين")
                return False
        else:
            print("❌ جدول امتحانات غير موجود")
            return False
        
        # التحقق من جدول بيانات_المؤسسة للخلفية
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='بيانات_المؤسسة'")
        if cursor.fetchone():
            print("✅ جدول بيانات_المؤسسة موجود")
            
            # التحقق من عمود الخلفية
            cursor.execute("PRAGMA table_info(بيانات_المؤسسة)")
            columns = [column[1] for column in cursor.fetchall()]
            
            if 'ImagePath2' in columns:
                print("✅ عمود ImagePath2 موجود للخلفية")
                
                # البحث عن خلفية
                cursor.execute("SELECT ImagePath2 FROM بيانات_المؤسسة WHERE ImagePath2 IS NOT NULL AND ImagePath2 != '' LIMIT 1")
                result = cursor.fetchone()
                
                if result and result[0]:
                    background_path = result[0]
                    print(f"🎨 خلفية محفوظة: {background_path}")
                    
                    if os.path.exists(background_path):
                        print("✅ ملف الخلفية موجود")
                    else:
                        print("❌ ملف الخلفية غير موجود على القرص")
                else:
                    print("ℹ️ لا توجد خلفية محفوظة")
            else:
                print("⚠️ عمود ImagePath2 غير موجود - لن تتوفر خلفية")
        else:
            print("❌ جدول بيانات_المؤسسة غير موجود")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False
    
    return True

def test_improvements_applied():
    """اختبار التحسينات المُطبقة على الاستدعاءات"""
    print("\n🔧 اختبار التحسينات المُطبقة على استدعاءات المترشحين...")
    
    # التحقق من تعطيل الفتح التلقائي في print12.py
    try:
        with open('print12.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        if 'لن يتم فتحه تلقائياً لتطبيق الخلفية أولاً' in content:
            print("✅ تم تعطيل الفتح التلقائي في print12.py")
        else:
            print("❌ لم يتم تعطيل الفتح التلقائي في print12.py")
            
        if 'os.startfile(output_path)' in content and 'لا نفتح الملف تلقائياً' in content:
            print("✅ تم التحديث بشكل صحيح")
        elif 'os.startfile(output_path)' in content:
            print("⚠️ ما زال هناك فتح تلقائي في print12.py")
        else:
            print("✅ لا يوجد فتح تلقائي في print12.py")
            
    except Exception as e:
        print(f"❌ خطأ في فحص ملف print12.py: {e}")
    
    # التحقق من وجود التحديثات في دالة candidate_calls
    try:
        with open('sub40_window.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        # التحقق من وجود التحديثات في دالة candidate_calls
        if 'apply_background_to_existing_pdf(output_path, background_path)' in content:
            print("✅ تم تحديث دالة candidate_calls لاستخدام الطريقة المحسنة")
        else:
            print("❌ لم يتم تحديث دالة candidate_calls")
            
        # التحقق من وجود آلية التحقق من سلامة الملف
        if 'verify_report_file(output_path)' in content:
            print("✅ تم إضافة التحقق من سلامة الملف")
        else:
            print("❌ لم يتم إضافة التحقق من سلامة الملف")
            
        # التحقق من رسائل النجاح المحسنة
        if 'مع تطبيق الخلفية المحفوظة' in content:
            print("✅ تم تحديث رسائل النجاح لتشمل معلومات الخلفية")
        else:
            print("❌ لم يتم تحديث رسائل النجاح")
            
    except Exception as e:
        print(f"❌ خطأ في فحص ملف sub40_window.py: {e}")

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 70)
    print("🧪 اختبار شامل لاستدعاءات المترشحين المحسنة")
    print("=" * 70)
    
    print("📋 التحسينات المُطبقة على استدعاءات المترشحين:")
    print("   ✅ منع فتح التقرير قبل تطبيق الخلفية")
    print("   ✅ تطبيق خلفية بدون تراكم الطبقات")
    print("   ✅ التحقق من سلامة الملف قبل الفتح")
    print("   ✅ رسائل تشخيص مفصلة")
    print("   ✅ معالجة أخطاء شاملة")
    print("   ✅ اختيار طباعة حسب المؤسسة أو شاملة")
    print("   ✅ واجهة مستخدم محسنة للاختيار")
    print("   ✅ عرض إحصائيات مفصلة مع معلومات الخلفية")
    
    print("\n" + "-" * 70)
    
    # تشغيل الاختبارات
    setup_test = test_invitations_setup()
    improvements_test = test_improvements_applied()
    
    print("\n" + "=" * 70)
    print("📊 نتائج الاختبار:")
    print(f"   إعداد استدعاءات المترشحين: {'✅ نجح' if setup_test else '❌ فشل'}")
    print(f"   التحسينات المُطبقة: {'✅ مُطبقة' if improvements_test else '❌ غير مُطبقة'}")
    
    if setup_test:
        print("\n🎉 جميع الاختبارات نجحت!")
        print("💡 يمكنك الآن تجربة زر 'استدعاءات المترشحين' لاختبار التحسينات")
        print("📋 ما يجب أن تلاحظه:")
        print("   - نافذة اختيار أنيقة بين الطباعة الشاملة أو حسب المؤسسة")
        print("   - لن يُفتح التقرير تلقائياً أثناء المعالجة")
        print("   - ستطبق الخلفية بدون تراكم طبقات على جميع الصفحات")
        print("   - سيفتح التقرير النهائي فقط بعد اكتمال جميع العمليات")
        print("   - ستحصل على رسالة نجاح مفصلة مع معلومات الخلفية")
        print("   - جميع الصفحات ستكون متسقة ومتشابهة")
        print("   - يمكن طباعة استدعاءات مؤسسة محددة أو جميع المؤسسات")
    else:
        print("\n⚠️ بعض الاختبارات فشلت - يرجى مراجعة المشاكل أعلاه")
    
    print("=" * 70)

if __name__ == "__main__":
    main()
