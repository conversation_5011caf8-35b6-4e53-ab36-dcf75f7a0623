"""
ملف طباعة الاستدعاءات - print12.py
====================================

هذا الملف مسؤول عن طباعة استدعاءات المترشحين (استدعاء واحد في كل صفحة).

** المصدر الوحيد للبيانات **
جدول 'جدولة_الامتحان' في قاعدة البيانات هو المصدر الوحيد والموثوق
لجميع بيانات جدولة الامتحانات المستخدمة في هذا الملف.

جميع الاستعلامات تتم من خلال هذا الجدول فقط.

** التحكم في التخطيط **
- لتعديل المسافة بين رأس الصفحة وجدول العنوان، قم بتغيير قيمة HEADER_TO_TITLE_SPACING
- القيمة الحالية: 50 ملليمتر (يمكن زيادتها أو تقليلها حسب الحاجة)
"""

import os
import sys
import sqlite3
import traceback
from datetime import datetime
import subprocess

# ================= إعدادات عامة =================
# الجدول الأول: 4 أعمدة، 6 صفوف (بدلاً من 6 أعمدة وصفين)
COL_WIDTHS_TABLE1 = [70, 30, 70, 25]  # عرض الأعمدة الأربعة للجدول الأول
# الجدول الثالث (أستاذ، تخصص، توقيع) أسفل الصفحة
COL_WIDTHS_TABLE3 = [30, 40, 35]
# الجدول الرابع (رئيس المركز، توقيع) أسفل الصفحة
COL_WIDTHS_TABLE4 = [35, 45]

# إضافة إعدادات للهوامش (بالملليمتر)
PAGE_MARGIN_TOP = 0.2     # الهامش العلوي للصفحة
PAGE_MARGIN_BOTTOM = 0.2  # الهامش السفلي للصفحة
PAGE_MARGIN_LEFT = 10     # الهامش الأيسر للصفحة
PAGE_MARGIN_RIGHT = 10    # الهامش الأيمن للصفحة

# تحويل النقاط إلى ملليمتر
PT_TO_MM = 0.3528

# إعدادات أحجام المربعات النصية
LOGO_W_PT, LOGO_H_PT = 200, 80
BOX1_W_PT = 555  # مربع العنوان الرئيسي بعرض أكبر
TITLE_H_PT = 40

# تحويل الأحجام من نقاط إلى ملليمتر
LOGO_W = LOGO_W_PT * PT_TO_MM
LOGO_H = LOGO_H_PT * PT_TO_MM
BOX1_W = BOX1_W_PT * PT_TO_MM
BOX_H = TITLE_H_PT * PT_TO_MM

# إضافة ارتفاعات الصفوف
ROW_HEIGHT_TABLE1 = 10  # ارتفاع صفوف الجدول الأول
ROW_HEIGHT_HEADER = 14  # ارتفاع صفوف الرأس
# تقسيم ارتفاعات الجداول السفلية لكل جدول على حدة
ROW_HEIGHT_TABLE3_HEADER = 10  # ارتفاع رأس جدول الأستاذ
ROW_HEIGHT_TABLE3_DATA = 8    # ارتفاع صفوف بيانات جدول الأستاذ
ROW_HEIGHT_TABLE4_HEADER = 10  # ارتفاع رأس جدول رئيس المركز
ROW_HEIGHT_TABLE4_DATA = 16    # ارتفاع صفوف بيانات جدول رئيس المركز

# إعداد المسافة العمودية بين رأس الصفحة وجدول العنوان (قابلة للتحكم اليدوي)
# ===============================================
# لتعديل المسافة، قم بتغيير القيمة أدناه:
# - القيمة الحالية: 50 ملليمتر
# - لزيادة المسافة: استخدم قيمة أكبر (مثل 60 أو 70)
# - لتقليل المسافة: استخدم قيمة أصغر (مثل 30 أو 40)
# ===============================================
HEADER_TO_TITLE_SPACING = 50  # المسافة بالملليمتر - يمكن تعديلها حسب الحاجة

# إضافة إعدادات للهوامش (بالملليمتر)
PAGE_MARGIN_TOP = 0.2     # الهامش العلوي للصفحة
PAGE_MARGIN_BOTTOM = 0.2  # الهامش السفلي للصفحة
PAGE_MARGIN_LEFT = 10     # الهامش الأيسر للصفحة
PAGE_MARGIN_RIGHT = 10    # الهامش الأيمن للصفحة

PT_TO_MM = 0.3528
LOGO_W_PT, LOGO_H_PT = 200, 80
BOX1_W_PT, BOX2_W_PT, BOX3_W_PT = 300, 100, 150
TITLE_H_PT = 40
LOGO_W = LOGO_W_PT * PT_TO_MM
LOGO_H = LOGO_H_PT * PT_TO_MM
BOX1_W = BOX1_W_PT * PT_TO_MM
BOX2_W = BOX2_W_PT * PT_TO_MM
BOX3_W = BOX3_W_PT * PT_TO_MM
BOX_H = TITLE_H_PT * PT_TO_MM

try:
    from fpdf import FPDF
    import arabic_reshaper
    from bidi.algorithm import get_display
except ImportError:
    subprocess.check_call([sys.executable, "-m", "pip", "install", "fpdf2", "arabic-reshaper", "python-bidi"])
    from fpdf import FPDF
    import arabic_reshaper
    from bidi.algorithm import get_display

class ArabicPDF(FPDF):
    def __init__(self):
        super().__init__('P','mm','A4')
        # تعيين الهوامش
        self.set_margins(PAGE_MARGIN_LEFT, PAGE_MARGIN_TOP, PAGE_MARGIN_RIGHT)
        self.set_auto_page_break(auto=True, margin=PAGE_MARGIN_BOTTOM)
        fonts_dir = os.path.join(os.path.dirname(__file__), 'fonts')
        # تصحيح تحذير Deprecation Warning بإزالة المعامل uni=True
        self.add_font('Arial', '', os.path.join(fonts_dir, 'arial.ttf'))
        self.add_font('Arial', 'B', os.path.join(fonts_dir, 'arialbd.ttf'))
        self.set_font('Arial', '', 12)
        # تعيين سمك الخط الافتراضي
        self.set_line_width(0.5)

    def ar_text(self, txt: str) -> str:
        """
        تحويل النص العربي ليتم عرضه بشكل صحيح
        إذا كان النص يحتوي على رمز \n سيتم تجاهله وإرجاع نص مباشرة
        لأن fpdf تتعامل مع السطور الجديدة بشكل مختلف
        """
        reshaped = arabic_reshaper.reshape(str(txt))
        return get_display(reshaped)

    def multi_line_ar_text(self, txt: str, cell_width: float, font_size: int = 12) -> list:
        """
        تقسيم النص إلى سطور لتتناسب مع عرض الخلية

        المعاملات:
            txt: النص المراد تقسيمه
            cell_width: عرض الخلية بالملليمتر
            font_size: حجم الخط

        العوائد:
            قائمة بأسطر النص بعد التقسيم
        """
        lines = []
        # تقسيم النص إلى كلمات
        words = txt.split(' ')
        current_line = ""

        for word in words:
            # تقدير عرض السطر الحالي مع الكلمة المضافة
            test_line = current_line + " " + word if current_line else word
            # تحويل مؤقت للنص العربي لحساب العرض بشكل صحيح
            ar_test_line = self.ar_text(test_line)

            # حساب عرض النص التقريبي (استخدام تقدير بسيط)
            self.set_font('Arial', '', font_size)
            width = self.get_string_width(ar_test_line)

            # إذا تجاوز العرض المسموح، نضيف السطر الحالي ونبدأ بسطر جديد
            if width > cell_width and current_line:
                lines.append(current_line)
                current_line = word
            else:
                current_line = test_line

        # إضافة السطر الأخير إذا كان غير فارغ
        if current_line:
            lines.append(current_line)

        return lines


def fetch_records(db_path: str, filter_criteria=None):
    """
    جلب السجلات من قاعدة البيانات مع تطبيق معايير التصفية المحددة

    المعاملات:
        db_path: مسار قاعدة البيانات
        filter_criteria: معايير التصفية (اختياري)

    العوائد:
        (logo_path, records): مسار الشعار وقائمة السجلات
    """
    conn = sqlite3.connect(db_path)
    cur = conn.cursor()
    # جلب شعار المؤسسة
    cur.execute("SELECT ImagePath1 FROM بيانات_المؤسسة LIMIT 1")
    logo_row = cur.fetchone()
    logo_path = logo_row[0] if logo_row and os.path.exists(logo_row[0]) else None

    # إعداد الاستعلام مع مراعاة معايير التصفية
    query = '''
    SELECT رقم_الامتحان, الاسم_الكامل, الرمز, مركز_الامتحان, المستوى, القسم, القاعة, المؤسسة_الأصلية
    FROM امتحانات
    '''

    # إضافة شروط التصفية
    if filter_criteria:
        conditions = []
        params = []

        for key, value in filter_criteria.items():
            if value:
                conditions.append(f"{key} = ?")
                params.append(value)

        if conditions:
            query += " WHERE " + " AND ".join(conditions)

    # إضافة ترتيب السجلات
    query += " ORDER BY CAST(رقم_الامتحان AS INTEGER), CAST(القاعة AS INTEGER)"

    # تنفيذ الاستعلام
    if filter_criteria and params:
        cur.execute(query, params)
    else:
        cur.execute(query)

    cols = [c[0] for c in cur.description]
    recs = [dict(zip(cols, row)) for row in cur.fetchall()]
    conn.close()
    return logo_path, recs


def generate_report(logo_path, records, output_path, report_title=None, sub_title=None):
    """
    إنشاء تقرير المترشحين

    المعاملات:
        logo_path: مسار شعار المؤسسة
        records: سجلات المترشحين
        output_path: مسار ملف الخرج
        report_title: عنوان التقرير (اختياري)
        sub_title: العنوان الفرعي (اختياري)
    """
    pdf = ArabicPDF()
    margin = 10
    usable_w = pdf.w - 2 * margin

    # إذا لم يتم تحديد عنوان التقرير، نبحث عنه في قاعدة البيانات
    if not report_title:
        try:
            # البحث عن العنوان في جدولة_الامتحان - المصدر الوحيد
            db_path = os.path.join(os.path.dirname(__file__), 'data.db')
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT ملاحظات FROM جدولة_الامتحان WHERE id = 1 LIMIT 1")
            result = cursor.fetchone()
            conn.close()

            if result and result[0]:
                report_title = result[0]
        except Exception as e:
            print(f"خطأ في استرجاع عنوان التقرير من قاعدة البيانات: {str(e)}")

    # ترتيب السجلات حسب رقم الامتحان
    def get_exam_number(record):
        exam_number = record.get('رقم_الامتحان', '0')
        # التحقق من نوع البيانات
        if isinstance(exam_number, int):
            return exam_number
        elif isinstance(exam_number, str) and exam_number.isdigit():
            return int(exam_number)
        else:
            return 0

    records.sort(key=get_exam_number)

    # الطريقة العادية: استدعاء واحد في كل صفحة
    for rec in records:
        pdf.add_page()
        process_invitation(pdf, rec, logo_path, report_title, margin, usable_w, 0)

    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    pdf.output(output_path)
    print(f"تم إنشاء التقرير: {output_path}")
    return output_path

    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    pdf.output(output_path)
    print(f"تم إنشاء التقرير: {output_path}")
    return output_path


def process_invitation(pdf, rec, logo_path, report_title, margin, usable_w, y_offset=0):
    """
    معالجة استدعاء فردي
    
    المعاملات:
        pdf: كائن PDF
        rec: سجل المترشح
        logo_path: مسار الشعار
        report_title: عنوان التقرير
        margin: هامش الصفحة
        usable_w: العرض المتاح للصفحة        y_offset: إزاحة Y للاستدعاء
    """
    # الإعدادات الثابتة للاستدعاءات
    logo_height = LOGO_H
    font_size_title = 14
    font_size_text = 12
    font_size_table = 12
    table_row_height = ROW_HEIGHT_TABLE1
    
    # بداية موقع Y
    y = pdf.get_y() + y_offset
    
    # إضافة الشعار
    if logo_path and os.path.exists(logo_path):
        x_logo = (pdf.w - LOGO_W) / 2
        pdf.image(logo_path, x=x_logo, y=y, w=LOGO_W, h=logo_height)
        y += logo_height + 2
    
    # إضافة المسافة العمودية القابلة للتحكم اليدوي بين رأس الصفحة وجدول العنوان
    # يمكن تعديل قيمة HEADER_TO_TITLE_SPACING في أعلى الملف للتحكم في هذه المسافة
    spacing = HEADER_TO_TITLE_SPACING
    y += spacing
    
    # مربع العنوان الرئيسي
    pdf.set_draw_color(0,0,255)
    pdf.set_line_width(0.4)    
    # تحديد ارتفاع موحد للمربع العلوي
    box_height = 14
    
    # استخدام عنوان التقرير المخصص إذا كان متاحاً
    title_text = report_title if report_title else 'محضر بيانات الامتحانات'
    
    # تحديد موقع مربع العنوان في وسط الصفحة
    x = margin
    w = usable_w =195
    
    pdf.set_xy(x, y)
    pdf.set_font('Arial', 'B', font_size_title)
    
    # تقسيم النص إلى سطرين إذا كان طويلاً
    lines = pdf.multi_line_ar_text(title_text, w - 10, font_size_title)
    
    if len(lines) > 1:
        # رسم خلية فارغة أولاً
        pdf.cell(w, box_height, '', border=1, align='C')
          # ثم رسم كل سطر داخلها
        line_height = box_height / len(lines)
        for i, line in enumerate(lines[:2]):
            pdf.set_xy(x, y + i * line_height)
            pdf.cell(w, line_height, pdf.ar_text(line), border=0, align='C')
    else:
        # إذا كان النص قصيراً نرسمه كالمعتاد
        pdf.cell(w, box_height, pdf.ar_text(title_text), border=1, align='C')
    
    pdf.set_font('Arial', '', font_size_text)
    y += box_height + 2
    
    # الجدول الأول: 4 أعمدة، 4 صفوف (معكوسة الترتيب)
    cols1 = COL_WIDTHS_TABLE1
    
    # محاولة الحصول على قيمة المؤسسة الأصلية
    institution_value = rec.get('المؤسسة_الأصلية', '')
    if not institution_value:
        institution_value = rec.get('المؤسسة-الأصلية', '')
    
    table1_data = [
        [rec.get('الاسم_الكامل',''), 'الاسم الكامل', rec.get('مركز_الامتحان',''), 'مركز الامتحان'],
        [rec.get('الرمز',''), 'رقم المترشح (مسار)', institution_value, 'المؤسسة الأصلية'],
        [rec.get('المستوى',''), 'المستوى', rec.get('القاعة',''), 'قاعة الامتحان'],
        [rec.get('القسم',''), 'القسم', rec.get('رقم_الامتحان',''), 'رقم الامتحان']
    ]
    
    pdf.set_font('Arial', 'B', font_size_table)
    pdf.set_fill_color(230,230,230)
      # رسم جدول البيانات
    for row_idx, row_data in enumerate(table1_data):
        x = margin
        for col_idx, cell in enumerate(row_data):
            is_header = col_idx == 1 or col_idx == 3
            pdf.set_xy(x, y)
            pdf.cell(cols1[col_idx], table_row_height, pdf.ar_text(cell),
                    border=1, align='C', fill=is_header)
            x += cols1[col_idx]
        y += table_row_height


def print_exams_report(parent=None, level=None, report_title=None, sub_title=None, filter_criteria=None, output_dir=None):
    """
    دالة لإنشاء محضر توقيعات المترشحين، يمكن استدعاؤها من واجهات PyQt5

    المعاملات:
        parent: كائن النافذة الأم (لعرض رسائل)
        level: المستوى لتصفية البيانات (اختياري)
        report_title: عنوان المحضر (اختياري)
        sub_title: العنوان الفرعي (اختياري)
        filter_criteria: معايير التصفية الإضافية (اختياري)
        output_dir: مجلد حفظ التقرير (اختياري)

    العوائد:
        (success, output_path, message): ثلاثية تحدد نجاح العملية ومسار الملف ورسالة النتيجة
    """
    try:
        # تحديد مسار قاعدة البيانات
        db_path = os.path.join(os.path.dirname(__file__), 'data.db')

        # إعداد معايير التصفية
        criteria = filter_criteria or {}

        # تحديد مجلد حفظ التقرير
        if output_dir and os.path.exists(output_dir):
            reports_dir = output_dir
        else:
            # إنشاء مجلد التقارير الافتراضي إذا لم يتم تحديد مجلد
            reports_dir = os.path.join(os.path.expanduser('~'), 'Desktop', 'تقارير الامتحانات')

        # التأكد من وجود المجلد
        os.makedirs(reports_dir, exist_ok=True)

        # إضافة المستوى لمعايير التصفية إذا تم تحديده
        if level:
            criteria['المستوى'] = level

        # جلب السجلات
        logo_path, records = fetch_records(db_path, criteria)

        # التحقق من وجود سجلات
        if not records:
            return False, None, "لم يتم العثور على سجلات مطابقة."

        # تحديد اسم الملف بناءً على المعايير
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        # إضافة لاحقة للاسم بناءً على معايير التصفية
        suffix_parts = []

        # إضافة المستوى إذا تم تحديده
        if level:
            suffix_parts.append(f"{level}")

        # إضافة المؤسسة الأصلية إذا تم تحديدها
        if criteria.get('المؤسسة_الأصلية'):
            suffix_parts.append(f"{criteria['المؤسسة_الأصلية']}")        # إنشاء لاحقة الاسم
        file_suffix = "_".join(suffix_parts)
        if file_suffix:
            file_suffix = f"_{file_suffix}"

        # إنشاء اسم الملف
        output_path = os.path.join(reports_dir, f"استدعاءات_المترشحين{file_suffix}_{timestamp}.pdf")

        # إنشاء التقرير
        generate_report(logo_path, records, output_path, report_title, sub_title)

        # تطبيق خلفية PDF إذا كانت متاحة
        try:
            from background_applier import apply_background_if_available
            apply_background_if_available(db_path, output_path)
        except ImportError:
            print("ℹ️ وحدة تطبيق الخلفية غير متاحة")
        except Exception as e:
            print(f"⚠️ خطأ في تطبيق الخلفية: {e}")        # لا نفتح الملف تلقائياً - سيتم فتحه من الواجهة الرئيسية بعد تطبيق الخلفية
        print("ℹ️ تم إنشاء استدعاءات المترشحين بنجاح - لن يتم فتحه تلقائياً لتطبيق الخلفية أولاً")

        return True, output_path, "تم إنشاء التقرير بنجاح."
    except Exception as e:
        traceback.print_exc()
        return False, None, f"حدث خطأ: {str(e)}"


if __name__=='__main__':
    try:
        # اختبار إنشاء تقرير
        db = os.path.join(os.path.dirname(__file__), 'data.db')
        logo, recs = fetch_records(db)

        if not recs:
            print("لم يتم العثور على سجلات في قاعدة البيانات.")
            sys.exit(1)

        # الحصول على عنوان التقرير من المصدر الوحيد: جدولة_الامتحان
        report_title = None
        try:
            conn = sqlite3.connect(db)
            cursor = conn.cursor()
            cursor.execute("SELECT ملاحظات FROM جدولة_الامتحان WHERE id = 1 LIMIT 1")
            result = cursor.fetchone()
            conn.close()

            if result and result[0]:
                report_title = result[0]
        except Exception as e:
            print(f"خطأ في استرجاع عنوان التقرير: {str(e)}")

        out = os.path.join(os.path.expanduser('~'),'Desktop','تقارير الامتحانات', f"نموذج_محضر_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf")
        generate_report(logo, recs, out, report_title)
        print("تم إنشاء التقرير بنجاح.")
    except Exception as e:
        print(f"خطأ: {str(e)}")
        traceback.print_exc()
