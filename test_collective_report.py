#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تطبيق الحل المحسن على المحضر الجماعي للمترشحين
"""

import os
import sys

def test_collective_report_setup():
    """اختبار إعداد المحضر الجماعي"""
    print("🧪 اختبار إعداد المحضر الجماعي للمترشحين...")
    
    # التحقق من وجود ملفات البرنامج الأساسية
    required_files = [
        'sub40_window.py',
        'print313.py',
        'data.db'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ ملفات مفقودة: {', '.join(missing_files)}")
        return False
    
    print("✅ جميع الملفات المطلوبة موجودة")
    
    # اختبار استيراد الوحدات
    try:
        import print313
        print("✅ تم استيراد print313 بنجاح")
        
        # التحقق من وجود الدالة المطلوبة
        if hasattr(print313, 'print_exams_report'):
            print("✅ دالة print_exams_report موجودة في print313")
        else:
            print("❌ دالة print_exams_report غير موجودة في print313")
            return False
            
    except ImportError as e:
        print(f"❌ فشل في استيراد print313: {e}")
        return False
    
    # التحقق من قاعدة البيانات والبيانات المطلوبة للمحضر الجماعي
    try:
        import sqlite3
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # التحقق من وجود جدول امتحانات
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='امتحانات'")
        if cursor.fetchone():
            print("✅ جدول امتحانات موجود")
            
            # عد السجلات والمستويات
            cursor.execute("SELECT COUNT(*) FROM امتحانات")
            total_candidates = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(DISTINCT المستوى) FROM امتحانات WHERE المستوى IS NOT NULL AND المستوى != ''")
            total_levels = cursor.fetchone()[0]
            
            print(f"📊 عدد سجلات المترشحين: {total_candidates}")
            print(f"📊 عدد المستويات: {total_levels}")
            
            if total_candidates > 0:
                print("✅ توجد بيانات للمترشحين")
                
                # إحصائيات إضافية للمحضر الجماعي
                cursor.execute("SELECT COUNT(DISTINCT الجنس) FROM امتحانات WHERE الجنس IS NOT NULL AND الجنس != ''")
                gender_count = cursor.fetchone()[0]
                print(f"📊 أنواع الجنس المتوفرة: {gender_count}")
                
                cursor.execute("SELECT COUNT(DISTINCT \"المؤسسة_الأصلية\") FROM امتحانات WHERE \"المؤسسة_الأصلية\" IS NOT NULL AND \"المؤسسة_الأصلية\" != ''")
                institution_count = cursor.fetchone()[0]
                print(f"📊 عدد المؤسسات الأصلية: {institution_count}")
                
                # فحص تواريخ الميلاد
                cursor.execute("SELECT COUNT(*) FROM امتحانات WHERE تاريخ_الازدياد IS NOT NULL AND تاريخ_الازدياد != ''")
                birth_date_count = cursor.fetchone()[0]
                print(f"📊 عدد المترشحين بتواريخ ميلاد: {birth_date_count}")
                
                if total_levels > 1:
                    print("✅ متعدد المستويات - مناسب للمحضر الجماعي")
                else:
                    print("⚠️ مستوى واحد فقط - قد لا يكون مفيداً للمحضر الجماعي")
            else:
                print("❌ لا توجد بيانات للمترشحين")
                return False
        else:
            print("❌ جدول امتحانات غير موجود")
            return False
        
        # التحقق من جدول بيانات_المؤسسة للخلفية
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='بيانات_المؤسسة'")
        if cursor.fetchone():
            print("✅ جدول بيانات_المؤسسة موجود")
            
            # التحقق من عمود الخلفية
            cursor.execute("PRAGMA table_info(بيانات_المؤسسة)")
            columns = [column[1] for column in cursor.fetchall()]
            
            if 'ImagePath2' in columns:
                print("✅ عمود ImagePath2 موجود للخلفية")
                
                # البحث عن خلفية
                cursor.execute("SELECT ImagePath2 FROM بيانات_المؤسسة WHERE ImagePath2 IS NOT NULL AND ImagePath2 != '' LIMIT 1")
                result = cursor.fetchone()
                
                if result and result[0]:
                    background_path = result[0]
                    print(f"🎨 خلفية محفوظة: {background_path}")
                    
                    if os.path.exists(background_path):
                        print("✅ ملف الخلفية موجود")
                    else:
                        print("❌ ملف الخلفية غير موجود على القرص")
                else:
                    print("ℹ️ لا توجد خلفية محفوظة")
            else:
                print("⚠️ عمود ImagePath2 غير موجود - لن تتوفر خلفية")
        else:
            print("❌ جدول بيانات_المؤسسة غير موجود")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False
    
    return True

def test_improvements_applied():
    """اختبار التحسينات المُطبقة"""
    print("\n🔧 اختبار التحسينات المُطبقة على المحضر الجماعي...")
    
    # التحقق من تعطيل الفتح التلقائي في print313.py
    try:
        with open('print313.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        if 'لن يتم فتحه تلقائياً لتطبيق الخلفية أولاً' in content:
            print("✅ تم تعطيل الفتح التلقائي في print313.py")
        else:
            print("❌ لم يتم تعطيل الفتح التلقائي في print313.py")
            
        if 'os.startfile(output_path)' in content and 'لا نفتح الملف تلقائياً' in content:
            print("✅ تم التحديث بشكل صحيح")
        elif 'os.startfile(output_path)' in content:
            print("⚠️ ما زال هناك فتح تلقائي في print313.py")
        else:
            print("✅ لا يوجد فتح تلقائي في print313.py")
            
    except Exception as e:
        print(f"❌ خطأ في فحص ملف print313.py: {e}")
    
    # التحقق من وجود الدوال المطلوبة في sub40_window.py
    try:
        with open('sub40_window.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        required_methods = [
            'apply_background_to_existing_pdf',
            'verify_report_file',
            'get_background_path',
            'test_background_before_applying'
        ]
        
        missing_methods = []
        for method in required_methods:
            if f'def {method}' in content:
                print(f"✅ الدالة {method} موجودة")
            else:
                missing_methods.append(method)
        
        if missing_methods:
            print(f"❌ دوال مفقودة: {', '.join(missing_methods)}")
        else:
            print("✅ جميع الدوال المطلوبة موجودة")
            
        # التحقق من وجود التحديثات في دالة collective_report
        if 'apply_background_to_existing_pdf(output_path, background_path)' in content:
            print("✅ تم تحديث دالة collective_report لاستخدام الطريقة المحسنة")
        else:
            print("❌ لم يتم تحديث دالة collective_report")
            
    except Exception as e:
        print(f"❌ خطأ في فحص ملف sub40_window.py: {e}")

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 70)
    print("🧪 اختبار شامل للمحضر الجماعي للمترشحين المحسن")
    print("=" * 70)
    
    print("📋 التحسينات المُطبقة على المحضر الجماعي:")
    print("   ✅ منع فتح التقرير قبل تطبيق الخلفية")
    print("   ✅ تطبيق خلفية بدون تراكم الطبقات")
    print("   ✅ التحقق من سلامة الملف قبل الفتح")
    print("   ✅ رسائل تشخيص مفصلة")
    print("   ✅ معالجة أخطاء شاملة")
    print("   ✅ عرض إحصائيات مفصلة مع الخلفية")
    
    print("\n" + "-" * 70)
    
    # تشغيل الاختبارات
    setup_test = test_collective_report_setup()
    improvements_test = test_improvements_applied()
    
    print("\n" + "=" * 70)
    print("📊 نتائج الاختبار:")
    print(f"   إعداد المحضر الجماعي: {'✅ نجح' if setup_test else '❌ فشل'}")
    print(f"   التحسينات المُطبقة: {'✅ مُطبقة' if improvements_test else '❌ غير مُطبقة'}")
    
    if setup_test:
        print("\n🎉 جميع الاختبارات نجحت!")
        print("💡 يمكنك الآن تجربة زر 'المحضر الجماعي للمترشحين' لاختبار التحسينات")
        print("📋 ما يجب أن تلاحظه:")
        print("   - لن يُفتح التقرير تلقائياً أثناء المعالجة")
        print("   - ستطبق الخلفية بدون تراكم طبقات على جميع الصفحات")
        print("   - سيفتح التقرير النهائي فقط بعد اكتمال جميع العمليات")
        print("   - ستحصل على إحصائيات مفصلة مع معلومات الخلفية")
        print("   - جميع الصفحات ستكون متسقة ومتشابهة")
    else:
        print("\n⚠️ بعض الاختبارات فشلت - يرجى مراجعة المشاكل أعلاه")
    
    print("=" * 70)

if __name__ == "__main__":
    main()
