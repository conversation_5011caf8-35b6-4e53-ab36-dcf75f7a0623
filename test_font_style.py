#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تنسيق الخط الجديد - Calibri 12 غامق
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt

# إضافة مسار المجلد الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    """اختبار النافذة مع التنسيق الجديد للخط"""
    try:
        from sub26_window import ExamScheduleInstructions
        
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء النافذة
        window = ExamScheduleInstructions()
        window.show()
        
        print("🚀 تم فتح النافذة مع التنسيق الجديد!")
        print("\n✨ التحديثات على تنسيق الخط:")
        print("📝 الخط: Calibri حجم 12")
        print("🔤 الوزن: غامق (Bold)")
        print("🎨 اللون: أسود غامق (#2c3e50)")
        print("📏 المسافة بين الأسطر: 1.6")
        
        print("\n🎯 التحسينات المطبقة:")
        print("✅ CSS مخصص لعناصر القائمة (li)")
        print("✅ تنسيق موحد للفقرات (p)")
        print("✅ خط غامق لجميع القوائم (ul)")
        print("✅ لون أسود غامق لسهولة القراءة")
        print("✅ حجم خط مناسب (12px)")
        
        print("\n👀 للتحقق من التحسينات:")
        print("- تصفح النص في النافذة")
        print("- لاحظ وضوح وقوة الخط")
        print("- تحقق من سهولة القراءة")
        print("- قارن مع التنسيق السابق")
        
        print("\n🔧 الأزرار متاحة للاختبار:")
        print("- عرض الجدولة الحالية")
        print("- استيراد جدولة الامتحان (PDF)")
        print("- فتح مجلد الجدولة")
        print("- فتح نموذج استدعاء التلاميذ")
        
        # تشغيل التطبيق
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل النافذة: {e}")

if __name__ == "__main__":
    main()
