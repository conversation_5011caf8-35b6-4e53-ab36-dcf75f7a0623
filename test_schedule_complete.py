#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لنافذة جدولة الامتحان المحدثة
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import Qt

# إضافة مسار المجلد الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """اختبار استيراد المكتبات المطلوبة"""
    try:
        from sub26_window import ExamScheduleInstructions, ExamScheduleProfessional
        print("✅ تم استيراد الكلاسات بنجاح")
        return True
    except ImportError as e:
        print(f"❌ فشل في استيراد الكلاسات: {e}")
        return False

def test_database_structure():
    """اختبار هيكل قاعدة البيانات"""
    try:
        import sqlite3
        
        db_path = "data.db"
        if os.path.exists(db_path):
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # فحص الجداول
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [table[0] for table in cursor.fetchall()]
            
            print(f"📊 الجداول الموجودة: {tables}")
            
            # فحص جدول بيانات_المؤسسة
            if 'بيانات_المؤسسة' in tables:
                cursor.execute("PRAGMA table_info(بيانات_المؤسسة)")
                columns = [col[1] for col in cursor.fetchall()]
                print(f"📋 أعمدة جدول بيانات_المؤسسة: {columns}")
                
                if 'ImagePath2' in columns:
                    print("✅ عمود ImagePath2 موجود")
                else:
                    print("⚠️ عمود ImagePath2 غير موجود - سيتم إنشاؤه تلقائياً")
            else:
                print("⚠️ جدول بيانات_المؤسسة غير موجود - سيتم إنشاؤه تلقائياً")
            
            conn.close()
        else:
            print("⚠️ ملف قاعدة البيانات غير موجود - سيتم إنشاؤه تلقائياً")
        
        return True
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {e}")
        return False

def test_schedule_window():
    """اختبار نافذة تعليمات الجدولة"""
    try:
        from sub26_window import ExamScheduleInstructions
        
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء النافذة
        window = ExamScheduleInstructions()
        
        print("✅ تم إنشاء النافذة بنجاح")
        print(f"📐 حجم النافذة: {window.size().width()} x {window.size().height()}")
        print(f"📝 عنوان النافذة: {window.windowTitle()}")
        
        # عرض النافذة
        window.show()
        
        print("🚀 تم فتح نافذة تعليمات جدولة الامتحان بنجاح!")
        print("\nالأزرار المتاحة:")
        print("- عرض الجدولة الحالية")
        print("- استيراد جدولة الامتحان (PDF)")
        print("- فتح مجلد الجدولة")
        print("- فتح Microsoft Word")
        print("- إغلاق")
        print("- 🔍 (اختبار قاعدة البيانات)")
        
        # تشغيل التطبيق
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل النافذة: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🧪 بدء اختبار نافذة جدولة الامتحان المحدثة")
    print("=" * 50)
    
    # اختبار الاستيراد
    if not test_imports():
        return
    
    # اختبار قاعدة البيانات
    test_database_structure()
    
    print("=" * 50)
    
    # اختبار النافذة
    test_schedule_window()

if __name__ == "__main__":
    main()
