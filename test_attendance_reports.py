import sys
import os
import copy
from PyPDF2 import Pd<PERSON><PERSON><PERSON><PERSON>, PdfWriter, PageObject, Transformation
from PyQt5.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QPushButton,
    QFileDialog, QMessageBox
)
from PyQt5.QtCore import QUrl
from PyQt5.QtGui import QDesktopServices

def merge_two_up(input_path: str, output_path: str):
    """
    يدمج كل استدعائين في صفحة واحدة (2-up).
    يتوافق مع PyPDF2 ≥ 3.0.0.
    """
    reader = PdfReader(input_path)
    writer = PdfWriter()
    pages = list(reader.pages)

    # إذا كان عدد الصفحات فردياً، نضيف صفحة فارغة
    if len(pages) % 2 != 0:
        w0 = float(pages[-1].mediabox.width)
        h0 = float(pages[-1].mediabox.height)
        blank = PageObject.create_blank_page(width=w0, height=h0)
        pages.append(blank)

    # كل زوج من الصفحات => صفحة جديدة بعرض 2*w × h
    for i in range(0, len(pages), 2):
        left = pages[i]
        right = pages[i + 1]
        w = float(left.mediabox.width)
        h = float(left.mediabox.height)

        # إنشاء صفحة فارغة عريضة
        new_page = PageObject.create_blank_page(width=2 * w, height=h)

        # نلصق الصفحة الأولى (يسار)
        new_page.merge_page(left)

        # ننسخ الصفحة الثانية وندفعها يمينًا بمقدار w
        right_copy = copy.copy(right)
        right_copy.add_transformation(Transformation().translate(w, 0))
        new_page.merge_page(right_copy)

        writer.add_page(new_page)

    # حفظ PDF الناتج
    with open(output_path, "wb") as f_out:
        writer.write(f_out)

class TwoUpWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("دمج استدعائين في صفحة واحدة")
        self.resize(360, 120)

        layout = QVBoxLayout(self)
        btn = QPushButton("اختر تقرير PDF وابدأ المعالجة", self)
        btn.clicked.connect(self.process_pdf)
        layout.addWidget(btn)

    def process_pdf(self):
        # 1. اختيار الملف الأصلي
        in_pdf, _ = QFileDialog.getOpenFileName(
            self, "اختر تقرير PDF الأصلي", "", "ملفات PDF (*.pdf)"
        )
        if not in_pdf:
            return

        # 2. تحديد اسم ومكان حفظ الملف الناتج
        default_name = os.path.splitext(os.path.basename(in_pdf))[0] + "_2up.pdf"
        out_pdf, _ = QFileDialog.getSaveFileName(
            self, "حفظ التقرير الجديد", default_name, "ملفات PDF (*.pdf)"
        )
        if not out_pdf:
            return

        # 3. دمج الصفحات وحفظ الملف
        try:
            merge_two_up(in_pdf, out_pdf)
            QMessageBox.information(self, "تم", f"تم إنشاء التقرير:\n{out_pdf}")
            # 4. فتح الملف الناتج للمراجعة
            QDesktopServices.openUrl(QUrl.fromLocalFile(out_pdf))
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل المعالجة:\n{e}")

if __name__ == "__main__":
    # تأكد من تثبيت الحزم:
    # pip install PyQt5 PyPDF2
    app = QApplication(sys.argv)
    window = TwoUpWindow()
    window.show()
    sys.exit(app.exec_())
