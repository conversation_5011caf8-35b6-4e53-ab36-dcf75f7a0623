import sys
import os
import subprocess
import sqlite3
from PyQt5.QtWidgets import (QA<PERSON>lication, QWidget, QLabel, QLineEdit, QPushButton, 
                             QVBoxLayout, QHBoxLayout, QMessageBox, QTableWidget, QTableWidgetItem,
                             QSpinBox, QDateEdit, QGroupBox, QFormLayout, QComboBox, QStackedWidget,
                             QTextEdit, QScrollArea, QDialog, QDialogButtonBox, QListWidget, 
                             QListWidgetItem, QInputDialog)
from PyQt5.QtCore import QDate, Qt
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle
from reportlab.lib.pagesizes import A4, landscape, A3
from reportlab.lib import colors
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import arabic_reshaper
from bidi.algorithm import get_display
import json
from datetime import datetime

# تسجيل الخط العربي
try:
    pdfmetrics.registerFont(TTFont('Arabic', 'arial.ttf'))
except:
    print("تعذر تحميل الخط العربي")

def reshape_text(text):
    """تحويل النص العربي للعرض الصحيح"""
    reshaped = arabic_reshaper.reshape(text)
    return get_display(reshaped)

class UIController:
    """كلاس للتحكم في جميع خصائص العناصر يدوياً"""
    
    def __init__(self):
        # إعدادات الخطوط
        self.fonts = {
            'main_title': {'family': 'Calibri', 'size': 16, 'weight': 'bold', 'color': '#003366'},
            'step_title': {'family': 'Calibri', 'size': 16, 'weight': 'bold', 'color': '#003366'},
            'field_label': {'family': 'Calibri', 'size': 16, 'weight': 'bold', 'color': '#0066CC'},
            'input_text': {'family': 'Calibri', 'size': 16, 'weight': 'bold', 'color': 'black'},
            'button_text': {'family': 'Calibri', 'size': 16, 'weight': 'bold', 'color': 'white'},
            'detail_text': {'family': 'Calibri', 'size': 16, 'weight': 'bold', 'color': 'black'},
            'small_text': {'family': 'Calibri', 'size': 16, 'weight': 'bold', 'color': '#666666'}
        }
        
        # إعدادات الألوان
        self.colors = {
            'primary_bg': '#0066CC',
            'secondary_bg': '#4CAF50',
            'warning_bg': '#ff9800',
            'danger_bg': '#f44336',
            'light_bg': '#f0f8ff',
            'border_color': '#ccc',
            'disabled_bg': '#cccccc',
            'disabled_text': '#666666'
        }
        
        # إعدادات الأبعاد والمواضع (مصححة)
        self.dimensions = {
            # عناصر المرحلة الأولى
            'step1_title_label': {'width': 350, 'height': 80, 'x': 0, 'y': 0},
            'step1_title_input': {'width': 350, 'height': 40, 'x': 0, 'y': 0},
            'step1_exam_label': {'width': 350, 'height': 40, 'x': 0, 'y': 0},
            'step1_exam_input': {'width': 350, 'height': 40, 'x': 0, 'y': 0},
            'step1_rooms_label': {'width': 120, 'height': 40, 'x': 0, 'y': 0},
            'step1_rooms_spinbox': {'width': 80, 'height': 40, 'x': 0, 'y': 0},
            'step1_rooms_button': {'width': 150, 'height': 40, 'x': 0, 'y': 0},
            'step1_load_button': {'width': 150, 'height': 40, 'x': 0, 'y': 0},
            
            # عناصر المرحلة الثانية
            'step2_days_label': {'width': 120, 'height': 40, 'x': 0, 'y': 0},
            'step2_days_spinbox': {'width': 80, 'height': 40, 'x': 0, 'y': 0},
            'step2_dates_title': {'width': 350, 'height': 40, 'x': 0, 'y': 0},
            'step2_day_label': {'width': 120, 'height': 40, 'x': 0, 'y': 0},
            'step2_date_input': {'width': 200, 'height': 40, 'x': 0, 'y': 0},
            
            # عناصر المرحلة الثالثة
            'step3_nav_button': {'width': 120, 'height': 40, 'x': 0, 'y': 0},
            'step3_day_indicator': {'width': 200, 'height': 40, 'x': 0, 'y': 0},
            'step3_day_title': {'width': 400, 'height': 60, 'x': 0, 'y': 0},
            'step3_date_label': {'width': 400, 'height': 40, 'x': 0, 'y': 0},
            'step3_add_period_btn': {'width': 200, 'height': 40, 'x': 0, 'y': 0},
            'step3_period_combo': {'width': 200, 'height': 40, 'x': 0, 'y': 0},
            'step3_subject_combo': {'width': 200, 'height': 40, 'x': 0, 'y': 0},
            'step3_action_button': {'width': 100, 'height': 40, 'x': 0, 'y': 0},
            'step3_delete_button': {'width': 50, 'height': 40, 'x': 0, 'y': 0},
            'step3_final_button': {'width': 250, 'height': 40, 'x': 0, 'y': 0},
            
            # أزرار التنقل
            'nav_button': {'width': 100, 'height': 40, 'x': 0, 'y': 0}
        }
    
    def get_font_style(self, font_type):
        """الحصول على نمط الخط"""
        font = self.fonts.get(font_type, self.fonts['input_text'])
        weight = 'bold' if font['weight'] == 'bold' else 'normal'
        return f"""
            font-family: {font['family']};
            font-size: {font['size']}px;
            font-weight: {weight};
            color: {font['color']};
        """
    
    def get_button_style(self, bg_color_key='primary_bg', font_type='button_text'):
        """الحصول على نمط الزر"""
        font_style = self.get_font_style(font_type)
        bg_color = self.colors[bg_color_key]
        return f"""
            {font_style}
            background-color: {bg_color};
            padding: 0px;
            border: none;
            border-radius: 5px;
            margin: 3px;
        """
    
    def get_input_style(self, font_type='input_text'):
        """الحصول على نمط حقل الإدخال"""
        font_style = self.get_font_style(font_type)
        return f"""
            {font_style}
            padding: 0px;
            border: 2px solid {self.colors['border_color']};
            border-radius: 5px;
            margin: 3px;
        """
    
    def set_widget_properties(self, widget, element_name):
        """تطبيق خصائص العنصر"""
        if element_name in self.dimensions:
            dims = self.dimensions[element_name]
            widget.setFixedSize(dims['width'], dims['height'])
            # إزالة التحكم في الموضع لأنه يتعارض مع التخطيط
            # if dims['x'] != 0 or dims['y'] != 0:
            #     widget.move(dims['x'], dims['y'])

# إنشاء مثيل التحكم العام
ui_controller = UIController()

class StepWidget(QWidget):
    """ودجت أساسي للخطوات"""
    def __init__(self, title):
        super().__init__()
        self.title = title
        self.setup_ui()
    
    def setup_ui(self):
        main_layout = QVBoxLayout()
        
        # عنوان الخطوة في الأعلى
        title_label = QLabel(self.title)
        title_style = ui_controller.get_font_style('step_title') + """
            padding: 0px;
            margin: 5px;
            text-align: center;
            background-color: #f0f8ff;
            border: 1px solid #0066CC;
            border-radius: 5px;
        """
        title_label.setStyleSheet(title_style)
        main_layout.addWidget(title_label)
        
        # منطقة المحتوى القابلة للتمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: white;
            }
        """)
        
        # ودجت المحتوى
        content_widget = QWidget()
        self.content_layout = QVBoxLayout(content_widget)
        self.content_layout.setContentsMargins(10, 10, 10, 10)
        self.content_layout.setSpacing(10)
        
        scroll_area.setWidget(content_widget)
        main_layout.addWidget(scroll_area)
        
        # أزرار التنقل في الأسفل (ثابتة)
        nav_layout = QHBoxLayout()
        nav_layout.setContentsMargins(10, 5, 10, 10)
        
        self.prev_button = QPushButton("السابق")
        self.prev_button.setStyleSheet(ui_controller.get_button_style('primary_bg', 'button_text'))
        ui_controller.set_widget_properties(self.prev_button, 'nav_button')
        
        self.next_button = QPushButton("التالي")
        self.next_button.setStyleSheet(ui_controller.get_button_style('primary_bg', 'button_text'))
        ui_controller.set_widget_properties(self.next_button, 'nav_button')
        
        self.preview_button = QPushButton("معاينة")
        self.preview_button.setStyleSheet(ui_controller.get_button_style('secondary_bg', 'button_text'))
        ui_controller.set_widget_properties(self.preview_button, 'nav_button')
        
        self.generate_button = QPushButton("إنشاء PDF")
        self.generate_button.setStyleSheet(ui_controller.get_button_style('warning_bg', 'button_text'))
        ui_controller.set_widget_properties(self.generate_button, 'nav_button')
        
        nav_layout.addWidget(self.prev_button)
        nav_layout.addWidget(self.next_button)
        nav_layout.addStretch()
        nav_layout.addWidget(self.preview_button)
        nav_layout.addWidget(self.generate_button)
        
        main_layout.addLayout(nav_layout)
        self.setLayout(main_layout)

class RoomsDialog(QDialog):
    """نافذة حوارية لإدخال أرقام القاعات"""
    def __init__(self, parent=None, current_rooms=None):
        super().__init__(parent)
        self.setWindowTitle("إدخال أرقام القاعات")
        self.setModal(True)
        self.resize(400, 300)
        
        self.rooms_list = current_rooms or []
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout()
        
        # تعليمات
        instructions = QLabel("أدخل أرقام/أسماء القاعات (قاعة واحدة في كل مرة):")
        instructions.setStyleSheet(ui_controller.get_font_style('detail_text'))
        layout.addWidget(instructions)
        
        # قائمة القاعات
        self.rooms_listwidget = QListWidget()
        self.rooms_listwidget.setStyleSheet(ui_controller.get_input_style('input_text'))
        for room in self.rooms_list:
            self.rooms_listwidget.addItem(room)
        layout.addWidget(self.rooms_listwidget)
        
        # أزرار الإدارة
        buttons_layout = QHBoxLayout()
        
        self.add_button = QPushButton("إضافة قاعة")
        self.add_button.clicked.connect(self.add_room)
        self.add_button.setStyleSheet(ui_controller.get_button_style('secondary_bg'))
        
        self.edit_button = QPushButton("تعديل")
        self.edit_button.clicked.connect(self.edit_room)
        self.edit_button.setStyleSheet(ui_controller.get_button_style('warning_bg'))
        
        self.delete_button = QPushButton("حذف")
        self.delete_button.clicked.connect(self.delete_room)
        self.delete_button.setStyleSheet(ui_controller.get_button_style('danger_bg'))
        
        buttons_layout.addWidget(self.add_button)
        buttons_layout.addWidget(self.edit_button)
        buttons_layout.addWidget(self.delete_button)
        
        layout.addLayout(buttons_layout)
        
        # أزرار النافذة
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        # تطبيق الأنماط على أزرار النافذة
        for button in button_box.buttons():
            button.setStyleSheet(ui_controller.get_button_style('primary_bg'))
        layout.addWidget(button_box)
        
        self.setLayout(layout)
    
    def add_room(self):
        """إضافة قاعة جديدة"""
        room_name, ok = QInputDialog.getText(self, "إضافة قاعة", "اسم/رقم القاعة:")
        if ok and room_name.strip():
            self.rooms_listwidget.addItem(room_name.strip())
    
    def edit_room(self):
        """تعديل قاعة محددة"""
        current_item = self.rooms_listwidget.currentItem()
        if current_item:
            current_text = current_item.text()
            new_text, ok = QInputDialog.getText(self, "تعديل قاعة", "اسم/رقم القاعة:", text=current_text)
            if ok and new_text.strip():
                current_item.setText(new_text.strip())
    
    def delete_room(self):
        """حذف قاعة محددة"""
        current_row = self.rooms_listwidget.currentRow()
        if current_row >= 0:
            reply = QMessageBox.question(self, "تأكيد الحذف", 
                                       "هل تريد حذف هذه القاعة؟",
                                       QMessageBox.Yes | QMessageBox.No)
            if reply == QMessageBox.Yes:
                self.rooms_listwidget.takeItem(current_row)
    
    def get_rooms_list(self):
        """الحصول على قائمة القاعات"""
        rooms = []
        for i in range(self.rooms_listwidget.count()):
            rooms.append(self.rooms_listwidget.item(i).text())
        return rooms

class DatabaseManager:
    """مدير قاعدة البيانات"""
    def __init__(self, db_path="absence_grid.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """إنشاء قاعدة البيانات والجداول"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS saved_grids (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                title TEXT,
                exam_type TEXT,
                rooms_data TEXT,
                dates_data TEXT,
                subjects_data TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def save_grid(self, name, title, exam_type, rooms_data, dates_data, subjects_data):
        """حفظ شبكة جديدة"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO saved_grids (name, title, exam_type, rooms_data, dates_data, subjects_data)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (name, title, exam_type, 
              json.dumps(rooms_data), 
              json.dumps(dates_data), 
              json.dumps(subjects_data)))
        
        conn.commit()
        conn.close()
    
    def load_grid(self, grid_id):
        """تحميل شبكة محفوظة"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM saved_grids WHERE id = ?', (grid_id,))
        result = cursor.fetchone()
        
        conn.close()
        
        if result:
            return {
                'id': result[0],
                'name': result[1],
                'title': result[2],
                'exam_type': result[3],
                'rooms_data': json.loads(result[4]),
                'dates_data': json.loads(result[5]),
                'subjects_data': json.loads(result[6]),
                'created_at': result[7]
            }
        return None
    
    def get_all_grids(self):
        """الحصول على جميع الشبكات المحفوظة"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT id, name, created_at FROM saved_grids ORDER BY created_at DESC')
        results = cursor.fetchall()
        
        conn.close()
        
        return [{'id': r[0], 'name': r[1], 'created_at': r[2]} for r in results]
    
    def delete_grid(self, grid_id):
        """حذف شبكة محفوظة"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('DELETE FROM saved_grids WHERE id = ?', (grid_id,))
        
        conn.commit()
        conn.close()

class Step1BasicInfo(StepWidget):
    """الخطوة الأولى: المعلومات الأساسية"""
    def __init__(self):
        super().__init__("الخطوة الأولى: المعلومات الأساسية")
        
        # عدد القاعات
        self.rooms_count = 3
        
        # البطاقة الأولى - المعلومات الأساسية
        basic_info_card = QGroupBox("البيانات الأساسية")
        basic_info_card.setFixedHeight(300)
        basic_info_card.setStyleSheet(f"""
            QGroupBox {{
                {ui_controller.get_font_style('field_label')}
                border: 2px solid {ui_controller.colors['primary_bg']};
                border-radius: 10px;
                margin: 10px 5px;
                padding-top: 15px;
                background-color: white;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                right: 15px;
                padding: 0 10px 0 10px;
                background-color: white;
            }}
        """)
        
        basic_info_layout = QVBoxLayout()
        basic_info_layout.setAlignment(Qt.AlignRight)
        
        # صف العنوان
        title_row_layout = QHBoxLayout()
        title_row_layout.setDirection(QHBoxLayout.RightToLeft)
        
        title_label = QLabel("عنوان الشبكة:")
        title_label.setStyleSheet(ui_controller.get_font_style('field_label') + "padding: 0px; margin: 5px; text-align: right;")
        title_label.setFixedWidth(120)
        
        self.title_edit = QLineEdit("تتبع غياب المترشحات والمترشحين")
        self.title_edit.setStyleSheet(ui_controller.get_input_style('input_text'))
        self.title_edit.setFixedHeight(40)
        
        title_row_layout.addWidget(self.title_edit)
        title_row_layout.addWidget(title_label)
        title_row_layout.addStretch()
        
        # صف نوع الامتحان
        exam_row_layout = QHBoxLayout()
        exam_row_layout.setDirection(QHBoxLayout.RightToLeft)
        
        exam_label = QLabel("نوع الامتحان:")
        exam_label.setStyleSheet(ui_controller.get_font_style('field_label') + "padding: 0px; margin: 5px; text-align: right;")
        exam_label.setFixedWidth(120)
        
        self.exam_type_edit = QLineEdit("الامتحان الجهوي : أولى باك علوم ( الدورة العادية 2025 )")
        self.exam_type_edit.setStyleSheet(ui_controller.get_input_style('input_text'))
        self.exam_type_edit.setFixedHeight(40)
        
        exam_row_layout.addWidget(self.exam_type_edit)
        exam_row_layout.addWidget(exam_label)
        exam_row_layout.addStretch()
        
        # صف عدد القاعات
        rooms_row_layout = QHBoxLayout()
        rooms_row_layout.setDirection(QHBoxLayout.RightToLeft)
        
        rooms_label = QLabel("عدد القاعات:")
        rooms_label.setStyleSheet(ui_controller.get_font_style('field_label') + "padding: 0px; margin: 5px; text-align: right;")
        rooms_label.setFixedWidth(100)
        
        self.rooms_spinbox = QSpinBox()
        self.rooms_spinbox.setMinimum(1)
        self.rooms_spinbox.setMaximum(100)
        self.rooms_spinbox.setValue(self.rooms_count)
        self.rooms_spinbox.valueChanged.connect(self.update_rooms_count)
        self.rooms_spinbox.setStyleSheet(ui_controller.get_input_style('input_text'))
        self.rooms_spinbox.setFixedHeight(40)
        self.rooms_spinbox.setFixedWidth(80)
        
        rooms_row_layout.addWidget(self.rooms_spinbox)
        rooms_row_layout.addWidget(rooms_label)
        rooms_row_layout.addStretch()
        
        # إضافة الصفوف للبطاقة الأولى
        basic_info_layout.addWidget(QLabel())  # مساحة فارغة
        basic_info_layout.addLayout(title_row_layout)
        basic_info_layout.addWidget(QLabel())  # مساحة فارغة
        basic_info_layout.addLayout(exam_row_layout)
        basic_info_layout.addWidget(QLabel())  # مساحة فارغة
        basic_info_layout.addLayout(rooms_row_layout)
        basic_info_layout.addStretch()
        
        basic_info_card.setLayout(basic_info_layout)
        
        # إضافة البطاقة للمحتوى الرئيسي
        self.content_layout.addWidget(basic_info_card)
        self.content_layout.addStretch()
    
    def update_rooms_count(self):
        """تحديث عدد القاعات"""
        self.rooms_count = self.rooms_spinbox.value()
    
    def get_rooms_list(self):
        """الحصول على قائمة القاعات النهائية"""
        # إنشاء قائمة رقمية افتراضية
        return [str(i) for i in range(1, self.rooms_count + 1)]

class Step2DatesInput(StepWidget):
    """الخطوة الثانية: إدخال التواريخ"""
    def __init__(self):
        super().__init__("الخطوة الثانية: إدخال الأيام والتواريخ")
        
        # البطاقة الأولى - عدد الأيام
        days_count_card = QGroupBox("عدد الأيام")
        days_count_card.setFixedHeight(120)
        days_count_card.setStyleSheet(f"""
            QGroupBox {{
                {ui_controller.get_font_style('field_label')}
                border: 2px solid {ui_controller.colors['primary_bg']};
                border-radius: 10px;
                margin: 10px 5px;
                padding-top: 15px;
                background-color: white;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                right: 15px;
                padding: 0 10px 0 10px;
                background-color: white;
            }}
        """)
        
        days_count_layout = QVBoxLayout()
        days_count_layout.setAlignment(Qt.AlignCenter)
        
        # صف عدد الأيام
        days_row_layout = QHBoxLayout()
        days_row_layout.setDirection(QHBoxLayout.RightToLeft)
        
        days_label = QLabel("عدد الأيام:")
        days_label.setStyleSheet(ui_controller.get_font_style('field_label') + "padding: 0px; margin: 3px; text-align: right;")
        days_label.setFixedWidth(100)
        
        self.days_spinbox = QSpinBox()
        self.days_spinbox.setMinimum(1)
        self.days_spinbox.setMaximum(10)
        self.days_spinbox.setValue(2)
        self.days_spinbox.valueChanged.connect(self.update_date_inputs)
        self.days_spinbox.setStyleSheet(ui_controller.get_input_style('input_text'))
        self.days_spinbox.setFixedHeight(40)
        self.days_spinbox.setFixedWidth(80)
        
        days_row_layout.addWidget(self.days_spinbox)
        days_row_layout.addWidget(days_label)
        days_row_layout.addStretch()
        
        days_count_layout.addWidget(QLabel())  # مساحة فارغة
        days_count_layout.addLayout(days_row_layout)
        days_count_layout.addStretch()
        
        days_count_card.setLayout(days_count_layout)
        
        # البطاقة الثانية - الأيام والتواريخ
        dates_card = QGroupBox("الأيام والتواريخ")
        dates_card.setFixedHeight(300)
        dates_card.setStyleSheet(f"""
            QGroupBox {{
                {ui_controller.get_font_style('field_label')}
                border: 2px solid {ui_controller.colors['secondary_bg']};
                border-radius: 10px;
                margin: 10px 5px;
                padding-top: 15px;
                background-color: white;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                right: 15px;
                padding: 0 10px 0 10px;
                background-color: white;
            }}
        """)
        
        dates_layout = QVBoxLayout()
        dates_layout.setAlignment(Qt.AlignRight)
        
        # منطقة قابلة للتمرير للتواريخ
        dates_scroll = QScrollArea()
        dates_scroll_widget = QWidget()
        self.dates_layout = QVBoxLayout(dates_scroll_widget)
        dates_scroll.setWidget(dates_scroll_widget)
        dates_scroll.setWidgetResizable(True)
        dates_scroll.setStyleSheet("""
            QScrollArea {
                border: 1px solid #ddd;
                border-radius: 5px;
                background-color: white;
            }
        """)
        
        dates_layout.addWidget(dates_scroll)
        dates_card.setLayout(dates_layout)
        
        # البطاقة الثالثة - الأزرار
        buttons_card = QGroupBox("إجراءات")
        buttons_card.setFixedHeight(120)
        buttons_card.setStyleSheet(f"""
            QGroupBox {{
                {ui_controller.get_font_style('field_label')}
                border: 2px solid {ui_controller.colors['danger_bg']};
                border-radius: 10px;
                margin: 10px 5px;
                padding-top: 15px;
                background-color: white;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                right: 15px;
                padding: 0 10px 0 10px;
                background-color: white;
            }}
        """)
        
        buttons_layout = QVBoxLayout()
        buttons_layout.setAlignment(Qt.AlignCenter)
        
        # أزرار الإجراءات
        actions_layout = QHBoxLayout()
        actions_layout.setDirection(QHBoxLayout.RightToLeft)
        
        self.clear_button = QPushButton("مسح جميع التواريخ")
        self.clear_button.clicked.connect(self.clear_all_dates)
        self.clear_button.setStyleSheet(ui_controller.get_button_style('danger_bg', 'button_text'))
        self.clear_button.setFixedHeight(40)
        self.clear_button.setFixedWidth(150)
        
        self.auto_fill_button = QPushButton("ملء تلقائي للتواريخ")
        self.auto_fill_button.clicked.connect(self.auto_fill_dates)
        self.auto_fill_button.setStyleSheet(ui_controller.get_button_style('secondary_bg', 'button_text'))
        self.auto_fill_button.setFixedHeight(40)
        self.auto_fill_button.setFixedWidth(150)
        
        actions_layout.addWidget(self.auto_fill_button)
        actions_layout.addWidget(self.clear_button)
        actions_layout.addStretch()
        
        buttons_layout.addWidget(QLabel())  # مساحة فارغة
        buttons_layout.addLayout(actions_layout)
        buttons_layout.addStretch()
        
        buttons_card.setLayout(buttons_layout)
        
        # إضافة البطاقات للمحتوى الرئيسي
        self.content_layout.addWidget(days_count_card)
        self.content_layout.addWidget(dates_card)
        self.content_layout.addWidget(buttons_card)
        self.content_layout.addStretch()
        
        # قوائم حفظ التواريخ
        self.date_widgets = []
        
        # تحديث التواريخ الأولي
        self.update_date_inputs()
    
    def update_date_inputs(self):
        """تحديث حقول إدخال التواريخ حسب عدد الأيام المختار"""
        # حذف الحقول السابقة
        for widget in self.date_widgets:
            widget.setParent(None)
        self.date_widgets.clear()
        
        # مسح التخطيطات السابقة
        while self.dates_layout.count():
            child = self.dates_layout.takeAt(0)
            if child.widget():
                child.widget().setParent(None)
        
        # إنشاء حقول جديدة
        num_days = self.days_spinbox.value()
        for i in range(num_days):
            # إنشاء ودجت التاريخ
            date_widget = self.create_date_widget(i)
            self.dates_layout.addWidget(date_widget)
            self.date_widgets.append(date_widget)
        
        # إضافة مساحة مرنة
        self.dates_layout.addStretch()
    
    def create_date_widget(self, day_index):
        """إنشاء ودجت تاريخ لليوم"""
        date_widget = QWidget()
        date_layout = QHBoxLayout()
        date_layout.setDirection(QHBoxLayout.RightToLeft)
        
        # تسمية اليوم
        day_label = QLabel(f"اليوم {day_index + 1}:")
        day_label.setStyleSheet(ui_controller.get_font_style('field_label') + "padding: 0px; margin: 3px; text-align: right;")
        day_label.setFixedWidth(80)
        
        # حقل التاريخ
        date_edit = QDateEdit()
        date_edit.setCalendarPopup(True)
        date_edit.setDate(QDate.currentDate().addDays(day_index))
        date_edit.setDisplayFormat("dd-MM-yyyy")
        date_edit.setStyleSheet(ui_controller.get_input_style('input_text'))
        date_edit.setFixedHeight(40)
        date_edit.setFixedWidth(150)
        
        date_layout.addWidget(date_edit)
        date_layout.addWidget(day_label)
        date_layout.addStretch()
        
        date_widget.setLayout(date_layout)
        return date_widget
    
    def get_dates_list(self):
        """الحصول على قائمة التواريخ المدخلة (مضاعفة لتشمل الفترات)"""
        dates = []
        for widget in self.date_widgets:
            date_edit = widget.findChild(QDateEdit)
            if date_edit:
                date_str = date_edit.date().toString("dd-MM-yyyy")
                # إضافة التاريخ مرتين (للفترة الصباحية وبعد الزوال)
                dates.append(date_str)  # الفترة الصباحية
                dates.append(date_str)  # فترة بعد الزوال
        return dates
    
    def clear_all_dates(self):
        """مسح جميع التواريخ"""
        reply = QMessageBox.question(self, "تأكيد المسح", 
                                   "هل تريد مسح جميع التواريخ؟",
                                   QMessageBox.Yes | QMessageBox.No)
        if reply == QMessageBox.Yes:
            for widget in self.date_widgets:
                date_edit = widget.findChild(QDateEdit)
                if date_edit:
                    date_edit.setDate(QDate.currentDate())
    
    def auto_fill_dates(self):
        """ملء التواريخ تلقائياً"""
        current_date = QDate.currentDate()
        for i, widget in enumerate(self.date_widgets):
            date_edit = widget.findChild(QDateEdit)
            if date_edit:
                date_edit.setDate(current_date.addDays(i))

class Step3SubjectsInput(StepWidget):
    """المرحلة الثالثة: إدخال المواد والفترات"""
    def __init__(self, step2_widget):
        super().__init__("المرحلة الثالثة: تنظيم الفترات والمواد حسب الأيام")
        self.step2_widget = step2_widget
        self.day_columns = []
        self.current_day_index = 0
          # قائمة المواد المتاحة
        self.available_subjects = [
            "اللغة العربية",
            "اللغة الأجنبية الأولى", 
            "اللغة الأجنبية الثانية",
            "اللغة الفرنسية",
            "التربية الإسلامية",
            "التاريخ والجغرافيا",
            "الاجتماعيات",
            "الرياضيات",
            "العلوم الفيزيائية",
            "علوم الحياة والأرض",
            "الفلسفة"
        ]
        
        # قائمة الفترات المتاحة
        self.available_periods = [
            "الفترة الصباحية",
            "فترة بعد الزوال"
        ]
        
        # البطاقة الأولى - اليوم والتاريخ
        day_navigation_card = QGroupBox("اليوم والتاريخ")
        day_navigation_card.setFixedHeight(120)
        day_navigation_card.setStyleSheet(f"""
            QGroupBox {{
                {ui_controller.get_font_style('field_label')}
                border: 2px solid {ui_controller.colors['primary_bg']};
                border-radius: 10px;
                margin: 10px 5px;
                padding-top: 15px;
                background-color: white;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                right: 15px;
                padding: 0 10px 0 10px;
                background-color: white;
            }}
        """)
        
        day_navigation_layout = QVBoxLayout()
        day_navigation_layout.setAlignment(Qt.AlignRight)
        
        # صف اليوم والتاريخ
        day_nav_row_layout = QHBoxLayout()
        day_nav_row_layout.setDirection(QHBoxLayout.RightToLeft)
        
        self.prev_day_button = QPushButton("اليوم السابق")
        self.prev_day_button.setStyleSheet(ui_controller.get_button_style('warning_bg', 'button_text'))
        self.prev_day_button.setFixedHeight(40)
        self.prev_day_button.setFixedWidth(120)
        self.prev_day_button.clicked.connect(self.go_to_previous_day)
        
        self.day_indicator = QLabel("اليوم 1 من 1")
        self.day_indicator.setStyleSheet(ui_controller.get_font_style('field_label') + """
            text-align: center;
            padding: 0px;
            margin: 5px;
        """)
        self.day_indicator.setFixedWidth(200)
        
        self.next_day_button = QPushButton("اليوم التالي")
        self.next_day_button.setStyleSheet(ui_controller.get_button_style('warning_bg', 'button_text'))
        self.next_day_button.setFixedHeight(40)
        self.next_day_button.setFixedWidth(120)
        self.next_day_button.clicked.connect(self.go_to_next_day)
        
        day_nav_row_layout.addWidget(self.next_day_button)
        day_nav_row_layout.addWidget(self.day_indicator)
        day_nav_row_layout.addWidget(self.prev_day_button)
        day_nav_row_layout.addStretch()
        
        day_navigation_layout.addWidget(QLabel())  # مساحة فارغة
        day_navigation_layout.addLayout(day_nav_row_layout)
        day_navigation_layout.addStretch()
        
        day_navigation_card.setLayout(day_navigation_layout)
        
        # حاوية البطاقتين المتقابلتين
        periods_container = QHBoxLayout()
        
        # البطاقة الثانية - فترة بعد الزوال (تم تحريكها للأول)
        afternoon_subjects_card = QGroupBox("فترة بعد الزوال")
        afternoon_subjects_card.setFixedHeight(400)
        afternoon_subjects_card.setStyleSheet(f"""
            QGroupBox {{
                {ui_controller.get_font_style('field_label')}
                border: 2px solid {ui_controller.colors['warning_bg']};
                border-radius: 10px;
                margin: 10px 5px;
                padding-top: 15px;
                background-color: white;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                right: 15px;
                padding: 0 10px 0 10px;
                background-color: white;
            }}
        """)
        
        self.afternoon_subjects_layout = QVBoxLayout()
        self.afternoon_subjects_layout.setAlignment(Qt.AlignRight)
        
        # منطقة قابلة للتمرير لفترة بعد الزوال
        afternoon_scroll = QScrollArea()
        afternoon_scroll_widget = QWidget()
        self.afternoon_subjects_content_layout = QVBoxLayout(afternoon_scroll_widget)
        afternoon_scroll.setWidget(afternoon_scroll_widget)
        afternoon_scroll.setWidgetResizable(True)
        afternoon_scroll.setStyleSheet("""
            QScrollArea {
                border: 1px solid #ddd;
                border-radius: 5px;
                background-color: white;
            }
        """)
        
        # زر إضافة مادة بعد الزوال
        add_afternoon_subject_btn = QPushButton("إضافة مادة بعد الزوال")
        add_afternoon_subject_btn.clicked.connect(lambda: self.add_subject_to_period("afternoon"))
        add_afternoon_subject_btn.setStyleSheet(ui_controller.get_button_style('warning_bg', 'button_text'))
        add_afternoon_subject_btn.setFixedHeight(40)
        
        self.afternoon_subjects_layout.addWidget(afternoon_scroll)
        self.afternoon_subjects_layout.addWidget(add_afternoon_subject_btn)
        afternoon_subjects_card.setLayout(self.afternoon_subjects_layout)
        
        # البطاقة الثالثة - الفترة الصباحية (تم تحريكها للثاني)
        morning_subjects_card = QGroupBox("الفترة الصباحية")
        morning_subjects_card.setFixedHeight(400)
        morning_subjects_card.setStyleSheet(f"""
            QGroupBox {{
                {ui_controller.get_font_style('field_label')}
                border: 2px solid {ui_controller.colors['secondary_bg']};
                border-radius: 10px;
                margin: 10px 5px;
                padding-top: 15px;
                background-color: white;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                right: 15px;
                padding: 0 10px 0 10px;
                background-color: white;
            }}
        """)
        
        self.morning_subjects_layout = QVBoxLayout()
        self.morning_subjects_layout.setAlignment(Qt.AlignRight)
        
        # منطقة قابلة للتمرير للفترة الصباحية
        morning_scroll = QScrollArea()
        morning_scroll_widget = QWidget()
        self.morning_subjects_content_layout = QVBoxLayout(morning_scroll_widget)
        morning_scroll.setWidget(morning_scroll_widget)
        morning_scroll.setWidgetResizable(True)
        morning_scroll.setStyleSheet("""
            QScrollArea {
                border: 1px solid #ddd;
                border-radius: 5px;
                background-color: white;
            }
        """)
        
        # زر إضافة مادة صباحية
        add_morning_subject_btn = QPushButton("إضافة مادة صباحية")
        add_morning_subject_btn.clicked.connect(lambda: self.add_subject_to_period("morning"))
        add_morning_subject_btn.setStyleSheet(ui_controller.get_button_style('secondary_bg', 'button_text'))
        add_morning_subject_btn.setFixedHeight(40)
        
        self.morning_subjects_layout.addWidget(morning_scroll)
        self.morning_subjects_layout.addWidget(add_morning_subject_btn)
        morning_subjects_card.setLayout(self.morning_subjects_layout)
        
        # إضافة البطاقتين المتقابلتين (بالترتيب الجديد: بعد الزوال أولاً، ثم الصباحية)
        periods_container.addWidget(afternoon_subjects_card)
        periods_container.addWidget(morning_subjects_card)
        
        # البطاقة الرابعة - الأزرار
        actions_card = QGroupBox("إجراءات")
        actions_card.setFixedHeight(200)
        actions_card.setStyleSheet(f"""
            QGroupBox {{
                {ui_controller.get_font_style('field_label')}
                border: 2px solid {ui_controller.colors['danger_bg']};
                border-radius: 10px;
                margin: 10px 5px;
                padding-top: 15px;
                background-color: white;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                right: 15px;
                padding: 0 10px 0 10px;
                background-color: white;
            }}
        """)
        
        actions_layout = QVBoxLayout()
        actions_layout.setAlignment(Qt.AlignRight)
        
        # أزرار الإجراءات
        final_actions_layout = QHBoxLayout()
        final_actions_layout.setDirection(QHBoxLayout.RightToLeft)
        
        self.save_button = QPushButton("حفظ البيانات وعرض الملخص")
        self.save_button.clicked.connect(self.save_and_show_summary)
        self.save_button.setStyleSheet(ui_controller.get_button_style('secondary_bg', 'button_text'))
        self.save_button.setFixedHeight(40)
        self.save_button.setFixedWidth(250)
        
        final_actions_layout.addWidget(self.save_button)
        final_actions_layout.addStretch()
        
        actions_layout.addWidget(QLabel())  # مساحة فارغة
        actions_layout.addLayout(final_actions_layout)
        actions_layout.addStretch()
        
        actions_card.setLayout(actions_layout)
        
        # تجميع البطاقات
        periods_widget = QWidget()
        periods_widget.setLayout(periods_container)
        
        # إضافة البطاقات للمحتوى الرئيسي
        self.content_layout.addWidget(day_navigation_card)
        self.content_layout.addWidget(periods_widget)
        self.content_layout.addWidget(actions_card)
        self.content_layout.addStretch()
        
        # قوائم حفظ البيانات
        self.morning_subjects = []
        self.afternoon_subjects = []
    
    def update_columns_layout(self):
        """تحديث تخطيط الأعمدة حسب عدد الأيام"""
        # حذف المواد السابقة
        self.clear_subjects_layouts()
        
        # الحصول على التواريخ الفريدة فقط (بدون تكرار للفترات)
        date_widgets = self.step2_widget.date_widgets
        
        if not date_widgets:
            return
        
        # إنشاء بيانات الأيام الفعلية فقط
        self.day_columns = []
        for i, widget in enumerate(date_widgets):
            date_edit = widget.findChild(QDateEdit)
            if date_edit:
                date_str = date_edit.date().toString("dd-MM-yyyy")
                self.day_columns.append({
                    'date': date_str,
                    'morning_subjects': [],
                    'afternoon_subjects': []
                })
        
        # إعادة تعيين اليوم الحالي
        self.current_day_index = 0
        self.update_day_navigation()
        self.load_current_day_subjects()

    def update_day_navigation(self):
        """تحديث أزرار التنقل ومؤشر اليوم"""
        total_days = len(self.day_columns)
        current_day = self.current_day_index + 1
        
        if total_days > 0:
            current_date = self.day_columns[self.current_day_index]['date']
            self.day_indicator.setText(f"اليوم {current_day} من {total_days} - {current_date}")
        else:
            self.day_indicator.setText("لا توجد أيام")
        
        # تحديث حالة الأزرار
        self.prev_day_button.setEnabled(self.current_day_index > 0)
        self.next_day_button.setEnabled(self.current_day_index < total_days - 1)

    def get_all_data(self):
        """الحصول على جميع البيانات"""
        # حفظ بيانات اليوم الحالي قبل الحصول على البيانات
        self.save_current_day_subjects()
        
        all_data = []
        
        for day_column in self.day_columns:
            date = day_column['date']
            periods_data = []
            
            # إضافة الفترة الصباحية
            if day_column['morning_subjects']:
                morning_sessions = [subject['subject'] for subject in day_column['morning_subjects']]
                periods_data.append({
                    'period_type': 'الفترة الصباحية',
                    'sessions': morning_sessions
                })
            
            # إضافة فترة بعد الزوال
            if day_column['afternoon_subjects']:
                afternoon_sessions = [subject['subject'] for subject in day_column['afternoon_subjects']]
                periods_data.append({
                    'period_type': 'فترة بعد الزوال',
                    'sessions': afternoon_sessions
                })
            
            all_data.append({
                'date': date,
                'periods': periods_data
            })
        
        return all_data
    
    def clear_subjects_layouts(self):
        """مسح جميع المواد من التخطيطات"""
        # مسح الفترة الصباحية
        while self.morning_subjects_content_layout.count():
            child = self.morning_subjects_content_layout.takeAt(0)
            if child.widget():
                child.widget().setParent(None)
        
        # مسح فترة بعد الزوال
        while self.afternoon_subjects_content_layout.count():
            child = self.afternoon_subjects_content_layout.takeAt(0)
            if child.widget():
                child.widget().setParent(None)
        
        self.morning_subjects.clear()
        self.afternoon_subjects.clear()
    
    def load_current_day_subjects(self):
        """تحميل مواد اليوم الحالي"""
        if not self.day_columns or self.current_day_index >= len(self.day_columns):
            return
        
        # مسح المواد الحالية
        self.clear_subjects_layouts()
        
        current_day = self.day_columns[self.current_day_index]
        
        # تحميل مواد الفترة الصباحية
        for subject_data in current_day['morning_subjects']:
            self.create_subject_widget("morning", subject_data['subject'])
        
        # تحميل مواد فترة بعد الزوال
        for subject_data in current_day['afternoon_subjects']:
            self.create_subject_widget("afternoon", subject_data['subject'])
        
        # إضافة مساحة مرنة
        self.morning_subjects_content_layout.addStretch()
        self.afternoon_subjects_content_layout.addStretch()
    
    def save_current_day_subjects(self):
        """حفظ مواد اليوم الحالي"""
        if not self.day_columns or self.current_day_index >= len(self.day_columns):
            return
        
        current_day = self.day_columns[self.current_day_index]
        
        # حفظ مواد الفترة الصباحية
        current_day['morning_subjects'] = []
        for subject_widget in self.morning_subjects:
            subject_combo = subject_widget.findChild(QComboBox)
            if subject_combo:
                subject_name = subject_combo.currentText()
                if subject_name != "اختر المادة...":
                    current_day['morning_subjects'].append({'subject': subject_name})
        
        # حفظ مواد فترة بعد الزوال
        current_day['afternoon_subjects'] = []
        for subject_widget in self.afternoon_subjects:
            subject_combo = subject_widget.findChild(QComboBox)
            if subject_combo:
                subject_name = subject_combo.currentText()
                if subject_name != "اختر المادة...":
                    current_day['afternoon_subjects'].append({'subject': subject_name})
    
    def go_to_previous_day(self):
        """الانتقال لليوم السابق"""
        if self.current_day_index > 0:
            self.save_current_day_subjects()
            self.current_day_index -= 1
            self.update_day_navigation()
            self.load_current_day_subjects()
    
    def go_to_next_day(self):
        """الانتقال لليوم التالي"""
        if self.current_day_index < len(self.day_columns) - 1:
            self.save_current_day_subjects()
            self.current_day_index += 1
            self.update_day_navigation()
            self.load_current_day_subjects()
    
    def add_subject_to_period(self, period_type):
        """إضافة مادة جديدة لفترة معينة"""
        if period_type == "morning":
            self.create_subject_widget("morning")
        elif period_type == "afternoon":
            self.create_subject_widget("afternoon")
    
    def create_subject_widget(self, period_type, selected_subject=None):
        """إنشاء ودجت مادة لفترة معينة"""
        subject_widget = QWidget()
        subject_layout = QHBoxLayout()
        subject_layout.setDirection(QHBoxLayout.RightToLeft)
        
        # رقم المادة
        if period_type == "morning":
            subject_number = len(self.morning_subjects) + 1
            subject_label = QLabel(f"المادة {subject_number}:")
        else:
            subject_number = len(self.afternoon_subjects) + 1
            subject_label = QLabel(f"المادة {subject_number}:")
        
        subject_label.setStyleSheet(ui_controller.get_font_style('detail_text') + "padding: 0px; margin: 3px; text-align: right;")
        subject_label.setFixedWidth(80)
        
        # اختيار المادة - هنا يمكنك التحكم في العرض
        subject_combo = QComboBox()
        subject_combo.addItems(["اختر المادة..."] + self.available_subjects)
        if selected_subject:
            index = subject_combo.findText(selected_subject)
            if index >= 0:
                subject_combo.setCurrentIndex(index)
        subject_combo.setStyleSheet(ui_controller.get_input_style('input_text'))
        subject_combo.setFixedHeight(40)
        # تحديد عرض مربع التحرير والسرد - غيّر هذا الرقم للتحكم في العرض
        subject_combo.setFixedWidth(200)  # يمكنك تغيير هذا الرقم (حالياً 200 بيكسل)
        
        # زر حذف المادة
        delete_subject_btn = QPushButton("حذف")
        delete_subject_btn.setStyleSheet(ui_controller.get_button_style('danger_bg', 'button_text'))
        delete_subject_btn.setFixedHeight(40)
        delete_subject_btn.setFixedWidth(60)
        delete_subject_btn.clicked.connect(lambda: self.delete_subject_widget(subject_widget, period_type))
        
        subject_layout.addWidget(delete_subject_btn)
        subject_layout.addWidget(subject_combo)
        subject_layout.addWidget(subject_label)
        subject_layout.addStretch()
        
        subject_widget.setLayout(subject_layout)
        
        # إضافة للتخطيط المناسب
        if period_type == "morning":
            # إزالة المساحة المرنة مؤقتاً
            if self.morning_subjects_content_layout.count() > 0:
                last_item = self.morning_subjects_content_layout.itemAt(self.morning_subjects_content_layout.count() - 1)
                if last_item.spacerItem():
                    self.morning_subjects_content_layout.removeItem(last_item)
            
            self.morning_subjects_content_layout.addWidget(subject_widget)
            self.morning_subjects_content_layout.addStretch()
            self.morning_subjects.append(subject_widget)
        else:
            # إزالة المساحة المرنة مؤقتاً
            if self.afternoon_subjects_content_layout.count() > 0:
                last_item = self.afternoon_subjects_content_layout.itemAt(self.afternoon_subjects_content_layout.count() - 1)
                if last_item.spacerItem():
                    self.afternoon_subjects_content_layout.removeItem(last_item)
            
            self.afternoon_subjects_content_layout.addWidget(subject_widget)
            self.afternoon_subjects_content_layout.addStretch()
            self.afternoon_subjects.append(subject_widget)
        
        self.update_subject_numbers(period_type)
    
    def delete_subject_widget(self, subject_widget, period_type):
        """حذف ودجت مادة"""
        subject_widget.setParent(None)
        
        if period_type == "morning":
            self.morning_subjects.remove(subject_widget)
        else:
            self.afternoon_subjects.remove(subject_widget)
        
        self.update_subject_numbers(period_type)
    
    def update_subject_numbers(self, period_type):
        """تحديث ترقيم المواد"""
        if period_type == "morning":
            subjects_list = self.morning_subjects
        else:
            subjects_list = self.afternoon_subjects
        
        for i, subject_widget in enumerate(subjects_list):
            subject_label = subject_widget.findChild(QLabel)
            if subject_label:
                subject_label.setText(f"المادة {i + 1}:")
    
    def get_all_data(self):
        """الحصول على جميع البيانات"""
        # حفظ بيانات اليوم الحالي قبل الحصول على البيانات
        self.save_current_day_subjects()
        
        all_data = []
        
        for day_column in self.day_columns:
            date = day_column['date']
            periods_data = []
            
            # إضافة الفترة الصباحية
            if day_column['morning_subjects']:
                morning_sessions = [subject['subject'] for subject in day_column['morning_subjects']]
                periods_data.append({
                    'period_type': 'الفترة الصباحية',
                    'sessions': morning_sessions
                })
            
            # إضافة فترة بعد الزوال
            if day_column['afternoon_subjects']:
                afternoon_sessions = [subject['subject'] for subject in day_column['afternoon_subjects']]
                periods_data.append({
                    'period_type': 'فترة بعد الزوال',
                    'sessions': afternoon_sessions
                })
            
            all_data.append({
                'date': date,
                'periods': periods_data
            })
        
        return all_data
    
    def save_and_show_summary(self):
        """حفظ البيانات وعرض الملخص"""
        data_summary = self.get_all_data()
        
        if not data_summary:
            QMessageBox.warning(self, "تحذير", "لا توجد بيانات لحفظها!")
            return
        
        summary_text = "ملخص البيانات المحفوظة:\n\n"
        
        for day_data in data_summary:
            date = day_data['date']
            periods = day_data['periods']
            
            summary_text += f"📅 التاريخ: {date}\n"
            
            if not periods:
                summary_text += "   - لا توجد فترات محددة\n"
            else:
                for period in periods:
                    period_type = period['period_type']
                    sessions = period['sessions']
                    
                    summary_text += f"   🕐 {period_type}:\n"
                    
                    if not sessions:
                        summary_text += "      - لا توجد مواد\n"
                    else:
                        for i, session in enumerate(sessions, 1):
                            subject = session if session != "اختر المادة..." else "غير محدد"
                            summary_text += f"      {i}. {subject}\n"
            
            summary_text += "\n"
        
        msg = QMessageBox(self)
        msg.setWindowTitle("ملخص البيانات المحفوظة")
        msg.setText("تم حفظ البيانات بنجاح!")
        msg.setDetailedText(summary_text)
        msg.setIcon(QMessageBox.Information)
        msg.exec_()
    
    def preview_table(self):
        """معاينة الجدول في نافذة منفصلة"""
        if self.current_step < 1:
            QMessageBox.warning(self, "تحذير", "يجب إكمال الخطوات الأساسية أولاً!")
            return
        
        try:
            table_data, num_cols, date_spans, period_spans, subject_spans = self.create_complete_table()
            
            # إنشاء نافذة منفصلة للمعاينة
            preview_dialog = QDialog(self)
            preview_dialog.setWindowTitle("معاينة الجدول")
            preview_dialog.resize(1000, 600)
            
            layout = QVBoxLayout()
            
            # إنشاء جدول المعاينة
            table_widget = QTableWidget()
            table_widget.setRowCount(len(table_data))
            table_widget.setColumnCount(num_cols)
            
            # تطبيق أنماط CSS للجدول مع محاذاة يمين ودعم النص العربي
            table_widget.setStyleSheet("""
                QTableWidget {
                    font-family: 'Arial', 'Tahoma', sans-serif;
                    font-size: 12px;
                    font-weight: bold;
                    direction: rtl;
                    text-align: right;
                    border: 2px solid #333;
                    gridline-color: #666;
                    background-color: white;
                }
                QTableWidget::item {
                    padding: 5px;
                    text-align: center;
                    border: 1px solid #666;
                    background-color: white;
                }
                QTableWidget::item:selected {
                    background-color: #4CAF50;
                    color: white;
                }
                QHeaderView::section {
                    background-color: #f0f0f0;
                    padding: 5px;
                    border: 1px solid #666;
                    font-weight: bold;
                    text-align: center;
                }
            """)
            
            # إخفاء رؤوس الصفوف والأعمدة
            table_widget.horizontalHeader().setVisible(False)
            table_widget.verticalHeader().setVisible(False)
            
            # ملء الجدول بالبيانات مع تطبيق تنسيق النص العربي
            for row_idx, row_data in enumerate(table_data):
                for col_idx, cell_data in enumerate(row_data):
                    # تطبيق تحويل النص العربي إذا لم يكن فارغاً
                    if cell_data and str(cell_data).strip():
                        formatted_text = reshape_text(str(cell_data))
                    else:
                        formatted_text = str(cell_data)
                    
                    item = QTableWidgetItem(formatted_text)
                    
                    # تطبيق محاذاة مركزية للنص
                    item.setTextAlignment(Qt.AlignCenter)
                    
                    # تطبيق ألوان خلفية وخطوط مختلفة للصفوف
                    from PyQt5.QtGui import QFont, QColor
                    
                    if row_idx == 0:  # العنوان - Calibri 18 أزرق غامق
                        item.setBackground(Qt.lightGray)
                        font = QFont("Calibri", 18, QFont.Bold)
                        item.setFont(font)
                        item.setForeground(QColor(0, 0, 139))  # أزرق غامق
                    elif row_idx == 1:  # نوع الامتحان - Calibri 17 أزرق غامق
                        item.setBackground(Qt.cyan)
                        font = QFont("Calibri", 17, QFont.Bold)
                        item.setFont(font)
                        item.setForeground(QColor(0, 0, 139))  # أزرق غامق
                    elif row_idx == 2:  # التواريخ - Calibri 16 أزرق غامق
                        item.setBackground(Qt.yellow)
                        font = QFont("Calibri", 16, QFont.Bold)
                        item.setFont(font)
                        item.setForeground(QColor(0, 0, 139))  # أزرق غامق
                    elif row_idx == 3:  # الفترات - Calibri 15 أزرق غامق
                        item.setBackground(Qt.lightGreen)
                        font = QFont("Calibri", 15, QFont.Bold)
                        item.setFont(font)
                        item.setForeground(QColor(0, 0, 139))  # أزرق غامق
                    elif row_idx == 4:  # المواد - Calibri 15 أسود غامق
                        item.setBackground(Qt.magenta)
                        font = QFont("Calibri", 15, QFont.Bold)
                        item.setFont(font)
                        item.setForeground(QColor(0, 0, 0))  # أسود غامق
                    elif row_idx == 5:  # الجنس - Calibri 14 أسود غامق
                        item.setBackground(Qt.lightBlue)
                        font = QFont("Calibri", 14, QFont.Bold)
                        item.setFont(font)
                        item.setForeground(QColor(0, 0, 0))  # أسود غامق
                    elif row_idx == len(table_data) - 1:  # صف المجموع (الصف الأخير)
                        item.setBackground(Qt.lightGray)
                        font = QFont("Calibri", 12, QFont.Bold)
                        item.setFont(font)
                        item.setForeground(QColor(0, 0, 0))  # أسود غامق
                    else:  # صفوف القاعات
                        # التحقق من عمود القاعات (آخر عمود)
                        if col_idx == num_cols - 1:  # عمود القاعات - Calibri 18 أسود غامق
                            font = QFont("Calibri", 18, QFont.Bold)
                            item.setFont(font)
                            item.setForeground(QColor(0, 0, 0))  # أسود غامق
                        else:  # خلايا القاعات العادية
                            font = QFont("Calibri", 12, QFont.Normal)
                            item.setFont(font)
                            item.setForeground(QColor(0, 0, 0))  # أسود غامق
                    
                    table_widget.setItem(row_idx, col_idx, item)
            
            # دمج الخلايا الأساسية
            table_widget.setSpan(0, 0, 1, num_cols)  # العنوان
            table_widget.setSpan(1, 0, 1, num_cols)  # نوع الامتحان
            
            # دمج خلايا التواريخ المتكررة
            for span_info in date_spans:
                start_col = span_info['start_col']
                span_count = span_info['span']
                table_widget.setSpan(2, start_col, 1, span_count)  # صف التواريخ
            
            # دمج خلايا الفترات المتكررة
            for span_info in period_spans:
                start_col = span_info['start_col']
                span_count = span_info['span']
                table_widget.setSpan(3, start_col, 1, span_count)  # صف الفترات
            
            # دمج خلايا المواد
            for span_info in subject_spans:
                start_col = span_info['start_col']
                span_count = span_info['span']
                table_widget.setSpan(4, start_col, 1, span_count)  # صف المواد
            
            # تحديد عرض الأعمدة
            for col in range(num_cols - 1):
                table_widget.setColumnWidth(col, 120)  # 120 بيكسل للمواد
            table_widget.setColumnWidth(num_cols - 1, 80)  # عمود القاعات (العمود الأخير)
            
            # تحديد ارتفاع الصفوف
            for row in range(table_widget.rowCount()):
                table_widget.setRowHeight(row, 40)
            
            # تطبيق اتجاه النافذة من اليمين لليسار
            preview_dialog.setLayoutDirection(Qt.RightToLeft)
            table_widget.setLayoutDirection(Qt.RightToLeft)
            
            layout.addWidget(table_widget)
            
            # أزرار النافذة
            buttons_layout = QHBoxLayout()
            buttons_layout.setDirection(QHBoxLayout.RightToLeft)
            
            close_button = QPushButton("إغلاق")
            close_button.setStyleSheet(ui_controller.get_button_style('danger_bg'))
            close_button.clicked.connect(preview_dialog.accept)
            
            buttons_layout.addWidget(close_button)
            buttons_layout.addStretch()
            
            layout.addLayout(buttons_layout)
            preview_dialog.setLayout(layout)
            
            preview_dialog.exec_()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في المعاينة: {str(e)}")
    
    def generate_pdf(self):
        """إنشاء ملف PDF"""
        try:
            table_data, num_cols, date_spans, period_spans, subject_spans = self.create_complete_table()
            
            file_path = "شبكة_غياب_متقدمة.pdf"
            doc = SimpleDocTemplate(file_path, pagesize=landscape(A3), rightMargin=22.68, leftMargin=22.68, topMargin=100, bottomMargin=22.68)
            
            # إضافة شعار المؤسسة في رأس الصفحة
            def add_header_with_logo(canvas, doc):
                # حفظ حالة الكانفاس
                canvas.saveState()
                
                # إضافة الشعار إذا كان متاحاً
                try:
                    # البحث عن شعار المؤسسة في قاعدة البيانات
                    db_path = os.path.join(os.path.dirname(__file__), 'data.db')
                    if os.path.exists(db_path):
                        conn = sqlite3.connect(db_path)
                        cursor = conn.cursor()
                        cursor.execute("SELECT ImagePath1 FROM بيانات_المؤسسة LIMIT 1")
                        logo_row = cursor.fetchone()
                        conn.close()
                        
                        if logo_row and logo_row[0] and os.path.exists(logo_row[0]):
                            logo_path = logo_row[0]
                            
                            # تحديد موضع وحجم الشعار في وسط الصفحة
                            page_width = landscape(A3)[0]  # 1190 نقطة
                            page_height = landscape(A3)[1]  # 842 نقطة
                            
                            # حجم مناسب للشعار
                            logo_width = 200  # عرض الشعار
                            logo_height = 80   # ارتفاع الشعار
                            
                            # وضع الشعار في منتصف عرض الصفحة وفي الجزء العلوي
                            logo_x = (page_width - logo_width) / 2  # توسيط أفقي
                            logo_y = page_height - logo_height - 15  # 15 نقطة من الحافة العلوية
                            
                            # رسم الشعار
                            canvas.drawImage(logo_path, logo_x, logo_y, width=logo_width, height=logo_height)
                            
                except Exception as e:
                    print(f"تعذر تحميل شعار المؤسسة: {str(e)}")
                
                # استعادة حالة الكانفاس
                canvas.restoreState()
            
            # تحديد عرض الأعمدة (زيادة العرض للاستفادة من المساحة الإضافية لورق A3)
            col_widths = [60] * (num_cols - 1) + [50]  # المواد 60 نقطة، عمود القاعات 70 نقطة (الأخير)
            table = Table(table_data, colWidths=col_widths)
            
            # تنسيق الجدول الأساسي
            table_style = [
                ('FONTNAME', (0, 0), (-1, -1), 'Arabic'),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('BOX', (0, 0), (-1, -1), 2, colors.black),
                ('INNERGRID', (0, 0), (-1, -1), 1, colors.black),
                
                # تنسيق الصف الأول - العنوان (Calibri 13 أزرق غامق)
                ('FONTSIZE', (0, 0), (num_cols-1, 0), 13),
                ('TEXTCOLOR', (0, 0), (num_cols-1, 0), colors.Color(0, 0, 139/255)),  # أزرق غامق
                ('BACKGROUND', (0, 0), (num_cols-1, 0), colors.lightgrey),
                
                # تنسيق الصف الثاني - نوع الامتحان (Calibri 13 أزرق غامق)
                ('FONTSIZE', (0, 1), (num_cols-1, 1), 13),
                ('TEXTCOLOR', (0, 1), (num_cols-1, 1), colors.Color(0, 0, 139/255)),  # أزرق غامق
                ('BACKGROUND', (0, 1), (num_cols-1, 1), colors.lightblue),
                
                # تنسيق الصف الثالث - التواريخ (Calibri 13 أزرق غامق)
                ('FONTSIZE', (0, 2), (num_cols-1, 2), 13),
                ('TEXTCOLOR', (0, 2), (num_cols-1, 2), colors.Color(0, 0, 139/255)),  # أزرق غامق
                ('BACKGROUND', (0, 2), (num_cols-1, 2), colors.lightyellow),
                
                # تنسيق الصف الرابع - الفترات (Calibri 13 أزرق غامق)
                ('FONTSIZE', (0, 3), (num_cols-1, 3), 13),
                ('TEXTCOLOR', (0, 3), (num_cols-1, 3), colors.Color(0, 0, 139/255)),  # أزرق غامق
                ('BACKGROUND', (0, 3), (num_cols-1, 3), colors.lightcyan),
                
                # تنسيق الصف الخامس - المواد (Calibri 13 أسود غامق)
                ('FONTSIZE', (0, 4), (num_cols-1, 4), 13),
                ('TEXTCOLOR', (0, 4), (num_cols-1, 4), colors.black),  # أسود غامق
                ('BACKGROUND', (0, 4), (num_cols-1, 4), colors.lightpink),
                
                # تنسيق الصف السادس - الجنس (Calibri 13 أسود غامق)
                ('FONTSIZE', (0, 5), (num_cols-1, 5), 13),
                ('TEXTCOLOR', (0, 5), (num_cols-1, 5), colors.black),  # أسود غامق
                ('BACKGROUND', (0, 5), (num_cols-1, 5), colors.lightgreen),
                
                ('SPAN', (0, 0), (num_cols-1, 0)),
                ('SPAN', (0, 1), (num_cols-1, 1)),
            ]
            
            # تنسيق صفوف القاعات (من الصف 6 فما فوق)
            num_room_rows = len(table_data) - 7  # تقليل 7 بدلاً من 6 للمجموع الجديد
            if num_room_rows > 0:
                # تنسيق عمود القاعات (آخر عمود) - Calibri 12 أسود غامق
                table_style.append(('FONTSIZE', (num_cols-1, 6), (num_cols-1, 5+num_room_rows), 12))
                table_style.append(('TEXTCOLOR', (num_cols-1, 6), (num_cols-1, 5+num_room_rows), colors.black))
                
                # تنسيق باقي خلايا القاعات - Calibri 12 أسود
                for col in range(num_cols-1):
                    table_style.append(('FONTSIZE', (col, 6), (col, 5+num_room_rows), 12))
                    table_style.append(('TEXTCOLOR', (col, 6), (col, 5+num_room_rows), colors.black))
            
            # تنسيق صف المجموع (الصف الأخير) - نفس تنسيق صفوف القاعات
            last_row_index = len(table_data) - 1
            # تنسيق عمود المجموع (آخر عمود) - Calibri 12 أسود غامق مع خلفية رمادية
            table_style.append(('FONTSIZE', (num_cols-1, last_row_index), (num_cols-1, last_row_index), 12))
            table_style.append(('TEXTCOLOR', (num_cols-1, last_row_index), (num_cols-1, last_row_index), colors.black))
            table_style.append(('BACKGROUND', (num_cols-1, last_row_index), (num_cols-1, last_row_index), colors.lightgrey))
            
            # تنسيق باقي خلايا صف المجموع - Calibri 12 أسود مع خلفية رمادية
            for col in range(num_cols-1):
                table_style.append(('FONTSIZE', (col, last_row_index), (col, last_row_index), 12))
                table_style.append(('TEXTCOLOR', (col, last_row_index), (col, last_row_index), colors.black))
                table_style.append(('BACKGROUND', (col, last_row_index), (col, last_row_index), colors.lightgrey))
            
            # إضافة دمج خلايا التواريخ المتكررة
            for span_info in date_spans:
                start_col = span_info['start_col']
                span_count = span_info['span']
                table_style.append(('SPAN', (start_col, 2), (start_col + span_count - 1, 2)))
            
            # إضافة دمج خلايا الفترات المتكررة
            for span_info in period_spans:
                start_col = span_info['start_col']
                span_count = span_info['span']
                table_style.append(('SPAN', (start_col, 3), (start_col + span_count - 1, 3)))
            
            # إضافة دمج خلايا المواد
            for span_info in subject_spans:
                start_col = span_info['start_col']
                span_count = span_info['span']
                table_style.append(('SPAN', (start_col, 4), (start_col + span_count - 1, 4)))
            
            table.setStyle(TableStyle(table_style))
            
            elements = [table]
            
            # إنشاء المستند مع دالة الرأس المخصصة
            doc.build(elements, onFirstPage=add_header_with_logo, onLaterPages=add_header_with_logo)
            
            # فتح الملف
            if sys.platform == 'win32':
                os.startfile(file_path)
            
            QMessageBox.information(self, "نجح", f"تم إنشاء الملف بنجاح!\nمسار الملف: {file_path}")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إنشاء الملف: {str(e)}")
class SimpleAbsenceGrid(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("إنشاء شبكة تتبع غياب المترشحين - النظام المبسط")
        self.resize(1200, 800)
        
        # تطبيق نمط عام للنافذة الرئيسية
        self.setStyleSheet(f"""
            QWidget {{
                background-color: {ui_controller.colors['light_bg']};
                {ui_controller.get_font_style('input_text')}
            }}
        """)
        
        # إنشاء النوافذ المكدسة
        self.stacked_widget = QStackedWidget()
        self.stacked_widget.setStyleSheet("""
            QStackedWidget {
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 5px;
            }
        """)
        
        # إنشاء الخطوات
        self.step1 = Step1BasicInfo()
        self.step2 = Step2DatesInput()
        self.step3 = Step3SubjectsInput(self.step2)
        
        # إضافة الخطوات للمكدس
        self.stacked_widget.addWidget(self.step1)
        self.stacked_widget.addWidget(self.step2)
        self.stacked_widget.addWidget(self.step3)
        
        # ربط الأزرار
        self.setup_navigation()
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.addWidget(self.stacked_widget)
        
        self.setLayout(main_layout)
        self.current_step = 0
        self.update_navigation_buttons()
    
    def setup_navigation(self):
        """ربط أزرار التنقل"""
        steps = [self.step1, self.step2, self.step3]
        
        for i, step in enumerate(steps):
            step.prev_button.clicked.connect(lambda checked, idx=i: self.go_to_step(idx - 1))
            step.next_button.clicked.connect(lambda checked, idx=i: self.go_to_step(idx + 1))
            step.preview_button.clicked.connect(self.preview_table)
            step.generate_button.clicked.connect(self.generate_pdf)
    
    def go_to_step(self, step_number):
        """الانتقال لخطوة معينة"""
        if 0 <= step_number < self.stacked_widget.count():
            # تحديث البيانات عند الانتقال للمرحلة الثالثة
            if step_number == 2:  # المرحلة الثالثة الجديدة
                self.step3.update_columns_layout()
            
            self.current_step = step_number
            self.stacked_widget.setCurrentIndex(step_number)
            self.update_navigation_buttons()
    
    def update_navigation_buttons(self):
        """تحديث حالة أزرار التنقل"""
        steps = [self.step1, self.step2, self.step3]
        
        for i, step in enumerate(steps):
            step.prev_button.setEnabled(i > 0)
            step.next_button.setEnabled(i < len(steps) - 1)
            step.preview_button.setEnabled(i >= 1)  # المعاينة من المرحلة الثانية
            step.generate_button.setEnabled(i == len(steps) - 1)  # PDF في المرحلة الأخيرة
    
    def create_complete_table(self):
        """إنشاء الجدول الكامل مع جميع البيانات ودمج التواريخ والفترات المتكررة"""
        # جمع البيانات
        title = reshape_text(self.step1.title_edit.text())
        exam_type = reshape_text(self.step1.exam_type_edit.text())
        rooms_list = self.step1.get_rooms_list()
        subjects_data = self.step3.get_all_data()
        
        # تحويل البيانات لصيغة مناسبة للجدول
        table_subjects_data = []
        for day_data in subjects_data:
            date = day_data['date']
            for period_data in day_data['periods']:
                period_type = period_data['period_type']
                for subject in period_data['sessions']:
                    if subject and subject != "اختر المادة...":
                        table_subjects_data.append({
                            'date': date,
                            'period': period_type,
                            'subject': subject
                        })
        
        # حساب إجمالي الأعمدة الضرورية فقط (بدون أعمدة فارغة إضافية)
        total_sessions = len(table_subjects_data)
        num_cols = total_sessions * 3 + 1  # 3 أعمدة لكل مادة + عمود القاعات
        
        # التأكد من الحد الأدنى للأعمدة
        if num_cols < 4:  # على الأقل عمود القاعات + 3 أعمدة
            num_cols = 4
        
        table_data = []
        
        # الصف الأول: العنوان
        title_row = [title] + [''] * (num_cols - 1)
        table_data.append(title_row)
        
        # الصف الثاني: نوع الامتحان
        exam_row = [exam_type] + [''] * (num_cols - 1)

        table_data.append(exam_row)
        
        # إعداد صفوف التواريخ والفترات والمواد والجنس
        dates_row = []
        periods_row = []
        subjects_row = []
        gender_row = []
        
        # عكس ترتيب البيانات (من اليمين لليسار)
        reversed_subjects_data = list(reversed(table_subjects_data))
        
        for data in reversed_subjects_data:
            # كل مادة تأخذ 3 خلايا
           
            dates_row.extend([reshape_text(data['date']), '', ''])
            periods_row.extend([reshape_text(data['period']), '', ''])
            subjects_row.extend([reshape_text(data['subject']), '', ''])
            # عكس ترتيب أعمدة الجنس: المجموع، أنثى، ذكر
            gender_row.extend([reshape_text('المجموع'), reshape_text('أنثى'), reshape_text('ذكر')])
        
        # إضافة عمود القاعات في النهاية (آخر عمود)
        dates_row.append(reshape_text('رقم القاعة'))
        periods_row.append('')
        subjects_row.append('')
        gender_row.append('')
        
        table_data.append(dates_row)
        table_data.append(periods_row)
        table_data.append(subjects_row)
        table_data.append(gender_row)
        
        # إضافة صفوف القاعات (بالترتيب الطبيعي)
        for room_name in rooms_list:
            # إنشاء صف للقاعة
            room_row = [''] * (num_cols - 1)  # خلايا فارغة للمواد
            room_row.append(str(room_name))  # رقم/اسم القاعة في العمود الأخير
            table_data.append(room_row)
        
        # إضافة صف المجموع أسفل صفوف القاعات
        total_row = [''] * (num_cols - 1)  # خلايا فارغة للمواد
        total_row.append(reshape_text('المجموع'))  # كلمة "المجموع" في العمود الأخير
        table_data.append(total_row)
        
        # حساب معلومات دمج التواريخ والفترات والمواد (بدون عمود القاعات)
        date_spans = self.calculate_date_spans_without_room_col(reversed_subjects_data)
        period_spans = self.calculate_period_spans_without_room_col(reversed_subjects_data)
        subject_spans = self.calculate_subject_spans_without_room_col(reversed_subjects_data)
        
        return table_data, num_cols, date_spans, period_spans, subject_spans

    def calculate_date_spans_without_room_col(self, subjects_data):
        """حساب نطاقات دمج التواريخ بدون مراعاة عمود القاعات"""
        date_spans = []
        if not subjects_data:
            return date_spans
        
        current_date = subjects_data[0]['date']
        start_col = 0
        count = 1
        
        for i in range(1, len(subjects_data)):
            if subjects_data[i]['date'] == current_date:
                count += 1
            else:
                date_spans.append({
                    'start_col': start_col * 3,
                    'span': count * 3,
                    'date': current_date
                })
                
                current_date = subjects_data[i]['date']
                start_col = i
                count = 1
        date_spans.append({
            'start_col': start_col * 3,
            'span': count * 3,
            'date': current_date
        })
        
        return date_spans

    def calculate_period_spans_without_room_col(self, subjects_data):
        """حساب نطاقات دمج الفترات بدون مراعاة عمود القاعات"""
        period_spans = []
        if not subjects_data:
            return period_spans
        
        current_period = subjects_data[0]['period']
        current_date = subjects_data[0]['date']
        start_col = 0
        count = 1
        
        for i in range(1, len(subjects_data)):
            if (subjects_data[i]['period'] == current_period and 
                subjects_data[i]['date'] == current_date):
                count += 1
            else:
                period_spans.append({
                    'start_col': start_col * 3,
                    'span': count * 3,
                    'period': current_period,
                    'date': current_date
                })
                
                current_period = subjects_data[i]['period']
                current_date = subjects_data[i]['date']
                start_col = i
                count = 1
        
        period_spans.append({
            'start_col': start_col * 3,
            'span': count * 3,
            'period': current_period,
            'date': current_date
        })
        
        return period_spans

    def calculate_subject_spans_without_room_col(self, subjects_data):
        """حساب نطاقات دمج المواد بدون مراعاة عمود القاعات"""
        subject_spans = []
        for i, data in enumerate(subjects_data):
            subject_spans.append({
                'start_col': i * 3,
                'span': 3,
                'subject': data['subject']
            })
        return subject_spans

    def preview_table(self):
        """معاينة الجدول في نافذة منفصلة"""
        if self.current_step < 1:
            QMessageBox.warning(self, "تحذير", "يجب إكمال الخطوات الأساسية أولاً!")
            return
        
        try:
            table_data, num_cols, date_spans, period_spans, subject_spans = self.create_complete_table()
            
            # إنشاء نافذة منفصلة للمعاينة
            preview_dialog = QDialog(self)
            preview_dialog.setWindowTitle("معاينة الجدول")
            preview_dialog.resize(1000, 600)
            
            layout = QVBoxLayout()
            
            # إنشاء جدول المعاينة
            table_widget = QTableWidget()
            table_widget.setRowCount(len(table_data))
            table_widget.setColumnCount(num_cols)
            
            # تطبيق أنماط CSS للجدول مع محاذاة يمين ودعم النص العربي
            table_widget.setStyleSheet("""
                QTableWidget {
                    font-family: 'Arial', 'Tahoma', sans-serif;
                    font-size: 12px;
                    font-weight: bold;
                    direction: rtl;
                    text-align: right;
                    border: 2px solid #333;
                    gridline-color: #666;
                    background-color: white;
                }
                QTableWidget::item {
                    padding: 5px;
                    text-align: center;
                    border: 1px solid #666;
                    background-color: white;
                }
                QTableWidget::item:selected {
                    background-color: #4CAF50;
                    color: white;
                }
                QHeaderView::section {
                    background-color: #f0f0f0;
                    padding: 5px;
                    border: 1px solid #666;
                    font-weight: bold;
                    text-align: center;
                }
            """)
            
            # إخفاء رؤوس الصفوف والأعمدة
            table_widget.horizontalHeader().setVisible(False)
            table_widget.verticalHeader().setVisible(False)
            
            # ملء الجدول بالبيانات مع تطبيق تنسيق النص العربي
            for row_idx, row_data in enumerate(table_data):
                for col_idx, cell_data in enumerate(row_data):
                    # تطبيق تحويل النص العربي إذا لم يكن فارغاً
                    if cell_data and str(cell_data).strip():
                        formatted_text = reshape_text(str(cell_data))
                    else:
                        formatted_text = str(cell_data)
                    
                    item = QTableWidgetItem(formatted_text)
                    
                    # تطبيق محاذاة مركزية للنص
                    item.setTextAlignment(Qt.AlignCenter)
                    
                    # تطبيق ألوان خلفية وخطوط مختلفة للصفوف
                    from PyQt5.QtGui import QFont, QColor
                    
                    if row_idx == 0:  # العنوان - Calibri 18 أزرق غامق
                        item.setBackground(Qt.lightGray)
                        font = QFont("Calibri", 18, QFont.Bold)
                        item.setFont(font)
                        item.setForeground(QColor(0, 0, 139))  # أزرق غامق
                    elif row_idx == 1:  # نوع الامتحان - Calibri 17 أزرق غامق
                        item.setBackground(Qt.cyan)
                        font = QFont("Calibri", 17, QFont.Bold)
                        item.setFont(font)
                        item.setForeground(QColor(0, 0, 139))  # أزرق غامق
                    elif row_idx == 2:  # التواريخ - Calibri 16 أزرق غامق
                        item.setBackground(Qt.yellow)
                        font = QFont("Calibri", 16, QFont.Bold)
                        item.setFont(font)
                        item.setForeground(QColor(0, 0, 139))  # أزرق غامق
                    elif row_idx == 3:  # الفترات - Calibri 15 أزرق غامق
                        item.setBackground(Qt.lightGreen)
                        font = QFont("Calibri", 15, QFont.Bold)
                        item.setFont(font)
                        item.setForeground(QColor(0, 0, 139))  # أزرق غامق
                    elif row_idx == 4:  # المواد - Calibri 15 أسود غامق
                        item.setBackground(Qt.magenta)
                        font = QFont("Calibri", 15, QFont.Bold)
                        item.setFont(font)
                        item.setForeground(QColor(0, 0, 0))  # أسود غامق
                    elif row_idx == 5:  # الجنس - Calibri 14 أسود غامق
                        item.setBackground(Qt.lightBlue)
                        font = QFont("Calibri", 14, QFont.Bold)
                        item.setFont(font)
                        item.setForeground(QColor(0, 0, 0))  # أسود غامق
                    elif row_idx == len(table_data) - 1:  # صف المجموع (الصف الأخير)
                        item.setBackground(Qt.lightGray)
                        font = QFont("Calibri", 12, QFont.Bold)
                        item.setFont(font)
                        item.setForeground(QColor(0, 0, 0))  # أسود غامق
                    else:  # صفوف القاعات
                        # التحقق من عمود القاعات (آخر عمود)
                        if col_idx == num_cols - 1:  # عمود القاعات - Calibri 18 أسود غامق
                            font = QFont("Calibri", 18, QFont.Bold)
                            item.setFont(font)
                            item.setForeground(QColor(0, 0, 0))  # أسود غامق
                        else:  # خلايا القاعات العادية
                            font = QFont("Calibri", 12, QFont.Normal)
                            item.setFont(font)
                            item.setForeground(QColor(0, 0, 0))  # أسود غامق
                    
                    table_widget.setItem(row_idx, col_idx, item)
            
            # دمج الخلايا الأساسية
            table_widget.setSpan(0, 0, 1, num_cols)  # العنوان
            table_widget.setSpan(1, 0, 1, num_cols)  # نوع الامتحان
            
            # دمج خلايا التواريخ المتكررة
            for span_info in date_spans:
                start_col = span_info['start_col']
                span_count = span_info['span']
                table_widget.setSpan(2, start_col, 1, span_count)  # صف التواريخ
            
            # دمج خلايا الفترات المتكررة
            for span_info in period_spans:
                start_col = span_info['start_col']
                span_count = span_info['span']
                table_widget.setSpan(3, start_col, 1, span_count)  # صف الفترات
            
            # دمج خلايا المواد
            for span_info in subject_spans:
                start_col = span_info['start_col']
                span_count = span_info['span']
                table_widget.setSpan(4, start_col, 1, span_count)  # صف المواد
              # تحديد عرض الأعمدة
            for col in range(num_cols - 1):
                table_widget.setColumnWidth(col, 120)  # 120 بيكسل للمواد
            table_widget.setColumnWidth(num_cols - 1, 80)  # عمود القاعات (العمود الأخير)
            
            # تحديد ارتفاع الصفوف
            for row in range(table_widget.rowCount()):
                table_widget.setRowHeight(row, 40)
            
            # تطبيق اتجاه النافذة من اليمين لليسار
            preview_dialog.setLayoutDirection(Qt.RightToLeft)
            table_widget.setLayoutDirection(Qt.RightToLeft)
            
            layout.addWidget(table_widget)
            
            # أزرار النافذة
            buttons_layout = QHBoxLayout()
            buttons_layout.setDirection(QHBoxLayout.RightToLeft)
            
            close_button = QPushButton("إغلاق")
            close_button.setStyleSheet(ui_controller.get_button_style('danger_bg'))
            close_button.clicked.connect(preview_dialog.accept)
            
            buttons_layout.addWidget(close_button)
            buttons_layout.addStretch()
            
            layout.addLayout(buttons_layout)
            preview_dialog.setLayout(layout)
            
            preview_dialog.exec_()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في المعاينة: {str(e)}")
    
    def generate_pdf(self):
        """إنشاء ملف PDF"""
        try:
            table_data, num_cols, date_spans, period_spans, subject_spans = self.create_complete_table()
            
            file_path = "شبكة_غياب_متقدمة.pdf"
            doc = SimpleDocTemplate(file_path, pagesize=landscape(A3), rightMargin=22.68, leftMargin=22.68, topMargin=100, bottomMargin=22.68)
            
            # إضافة شعار المؤسسة في رأس الصفحة
            def add_header_with_logo(canvas, doc):
                # حفظ حالة الكانفاس
                canvas.saveState()
                
                # إضافة الشعار إذا كان متاحاً
                try:
                    # البحث عن شعار المؤسسة في قاعدة البيانات
                    db_path = os.path.join(os.path.dirname(__file__), 'data.db')
                    if os.path.exists(db_path):
                        conn = sqlite3.connect(db_path)
                        cursor = conn.cursor()
                        cursor.execute("SELECT ImagePath1 FROM بيانات_المؤسسة LIMIT 1")
                        logo_row = cursor.fetchone()
                        conn.close()
                        
                        if logo_row and logo_row[0] and os.path.exists(logo_row[0]):
                            logo_path = logo_row[0]
                            
                            # تحديد موضع وحجم الشعار في وسط الصفحة
                            page_width = landscape(A3)[0]  # 1190 نقطة
                            page_height = landscape(A3)[1]  # 842 نقطة
                            
                            # حجم مناسب للشعار
                            logo_width = 200  # عرض الشعار
                            logo_height = 80   # ارتفاع الشعار
                            
                            # وضع الشعار في منتصف عرض الصفحة وفي الجزء العلوي
                            logo_x = (page_width - logo_width) / 2  # توسيط أفقي
                            logo_y = page_height - logo_height - 15  # 15 نقطة من الحافة العلوية
                            
                            # رسم الشعار
                            canvas.drawImage(logo_path, logo_x, logo_y, width=logo_width, height=logo_height)
                            
                except Exception as e:
                    print(f"تعذر تحميل شعار المؤسسة: {str(e)}")
                
                # استعادة حالة الكانفاس
                canvas.restoreState()
            
            # تحديد عرض الأعمدة (زيادة العرض للاستفادة من المساحة الإضافية لورق A3)
            col_widths = [60] * (num_cols - 1) + [40]  # المواد 60 نقطة، عمود القاعات 70 نقطة (الأخير)
            table = Table(table_data, colWidths=col_widths)
            
            # تنسيق الجدول الأساسي
            table_style = [
                ('FONTNAME', (0, 0), (-1, -1), 'Arabic'),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('BOX', (0, 0), (-1, -1), 2, colors.black),
                ('INNERGRID', (0, 0), (-1, -1), 1, colors.black),
                
                # تنسيق الصف الأول - العنوان (Calibri 13 أزرق غامق)
                ('FONTSIZE', (0, 0), (num_cols-1, 0), 13),
                ('TEXTCOLOR', (0, 0), (num_cols-1, 0), colors.Color(0, 0, 139/255)),  # أزرق غامق
                ('BACKGROUND', (0, 0), (num_cols-1, 0), colors.lightgrey),
                
                # تنسيق الصف الثاني - نوع الامتحان (Calibri 13 أزرق غامق)
                ('FONTSIZE', (0, 1), (num_cols-1, 1), 13),
                ('TEXTCOLOR', (0, 1), (num_cols-1, 1), colors.Color(0, 0, 139/255)),  # أزرق غامق
                ('BACKGROUND', (0, 1), (num_cols-1, 1), colors.lightblue),
                
                # تنسيق الصف الثالث - التواريخ (Calibri 13 أزرق غامق)
                ('FONTSIZE', (0, 2), (num_cols-1, 2), 13),
                ('TEXTCOLOR', (0, 2), (num_cols-1, 2), colors.Color(0, 0, 139/255)),  # أزرق غامق
                ('BACKGROUND', (0, 2), (num_cols-1, 2), colors.lightyellow),
                
                # تنسيق الصف الرابع - الفترات (Calibri 13 أزرق غامق)
                ('FONTSIZE', (0, 3), (num_cols-1, 3), 13),
                ('TEXTCOLOR', (0, 3), (num_cols-1, 3), colors.Color(0, 0, 139/255)),  # أزرق غامق
                ('BACKGROUND', (0, 3), (num_cols-1, 3), colors.lightcyan),
                
                # تنسيق الصف الخامس - المواد (Calibri 13 أسود غامق)
                ('FONTSIZE', (0, 4), (num_cols-1, 4), 13),
                ('TEXTCOLOR', (0, 4), (num_cols-1, 4), colors.black),  # أسود غامق
                ('BACKGROUND', (0, 4), (num_cols-1, 4), colors.lightpink),
                
                # تنسيق الصف السادس - الجنس (Calibri 13 أسود غامق)
                ('FONTSIZE', (0, 5), (num_cols-1, 5), 13),
                ('TEXTCOLOR', (0, 5), (num_cols-1, 5), colors.black),  # أسود غامق
                ('BACKGROUND', (0, 5), (num_cols-1, 5), colors.lightgreen),
                
                ('SPAN', (0, 0), (num_cols-1, 0)),
                ('SPAN', (0, 1), (num_cols-1, 1)),
            ]
            
            # تنسيق صفوف القاعات (من الصف 6 فما فوق)
            num_room_rows = len(table_data) - 7  # تقليل 7 بدلاً من 6 للمجموع الجديد
            if num_room_rows > 0:
                # تنسيق عمود القاعات (آخر عمود) - Calibri 12 أسود غامق
                table_style.append(('FONTSIZE', (num_cols-1, 6), (num_cols-1, 5+num_room_rows), 12))
                table_style.append(('TEXTCOLOR', (num_cols-1, 6), (num_cols-1, 5+num_room_rows), colors.black))
                
                # تنسيق باقي خلايا القاعات - Calibri 12 أسود
                for col in range(num_cols-1):
                    table_style.append(('FONTSIZE', (col, 6), (col, 5+num_room_rows), 12))
                    table_style.append(('TEXTCOLOR', (col, 6), (col, 5+num_room_rows), colors.black))
            
            # تنسيق صف المجموع (الصف الأخير) - نفس تنسيق صفوف القاعات
            last_row_index = len(table_data) - 1
            # تنسيق عمود المجموع (آخر عمود) - Calibri 12 أسود غامق مع خلفية رمادية
            table_style.append(('FONTSIZE', (num_cols-1, last_row_index), (num_cols-1, last_row_index), 12))
            table_style.append(('TEXTCOLOR', (num_cols-1, last_row_index), (num_cols-1, last_row_index), colors.black))
            table_style.append(('BACKGROUND', (num_cols-1, last_row_index), (num_cols-1, last_row_index), colors.lightgrey))
            
            # تنسيق باقي خلايا صف المجموع - Calibri 12 أسود مع خلفية رمادية
            for col in range(num_cols-1):
                table_style.append(('FONTSIZE', (col, last_row_index), (col, last_row_index), 12))
                table_style.append(('TEXTCOLOR', (col, last_row_index), (col, last_row_index), colors.black))
                table_style.append(('BACKGROUND', (col, last_row_index), (col, last_row_index), colors.lightgrey))
            
            # إضافة دمج خلايا التواريخ المتكررة
            for span_info in date_spans:
                start_col = span_info['start_col']
                span_count = span_info['span']
                table_style.append(('SPAN', (start_col, 2), (start_col + span_count - 1, 2)))
            
            # إضافة دمج خلايا الفترات المتكررة
            for span_info in period_spans:
                start_col = span_info['start_col']
                span_count = span_info['span']
                table_style.append(('SPAN', (start_col, 3), (start_col + span_count - 1, 3)))
            
            # إضافة دمج خلايا المواد
            for span_info in subject_spans:
                start_col = span_info['start_col']
                span_count = span_info['span']
                table_style.append(('SPAN', (start_col, 4), (start_col + span_count - 1, 4)))
            
            table.setStyle(TableStyle(table_style))
            
            elements = [table]
            
            # إنشاء المستند مع دالة الرأس المخصصة
            doc.build(elements, onFirstPage=add_header_with_logo, onLaterPages=add_header_with_logo)
            
            # فتح الملف
            if sys.platform == 'win32':
                os.startfile(file_path)
            
            QMessageBox.information(self, "نجح", f"تم إنشاء الملف بنجاح!\nمسار الملف: {file_path}")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إنشاء الملف: {str(e)}")
if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = SimpleAbsenceGrid()
    window.show()
    sys.exit(app.exec_())