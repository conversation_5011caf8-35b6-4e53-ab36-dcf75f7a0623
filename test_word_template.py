#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار وظائف نافذة جدولة الامتحان مع الزر المحدث
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import Qt

# إضافة مسار المجلد الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_word_template_functionality():
    """اختبار وظيفة نموذج استدعاء التلاميذ"""
    print("🧪 اختبار وظيفة نموذج استدعاء التلاميذ")
    print("=" * 50)
    
    # تحديد المسارات
    program_dir = os.path.dirname(os.path.abspath(__file__))
    schedule_folder = os.path.join(program_dir, "جدولة_الامتحان")
    word_template_path = os.path.join(schedule_folder, "نموذج استدعاء التلاميذ.docx")
    
    print(f"📁 مجلد البرنامج: {program_dir}")
    print(f"📁 مجلد الجدولة: {schedule_folder}")
    print(f"📄 مسار نموذج Word: {word_template_path}")
    
    # التحقق من وجود المجلد
    if os.path.exists(schedule_folder):
        print("✅ مجلد الجدولة موجود")
        
        # قائمة الملفات في المجلد
        files = os.listdir(schedule_folder)
        print(f"📂 ملفات المجلد: {files}")
        
        # التحقق من وجود نموذج Word
        if os.path.exists(word_template_path):
            print("✅ ملف نموذج استدعاء التلاميذ موجود")
            file_size = os.path.getsize(word_template_path)
            print(f"📊 حجم الملف: {file_size} بايت")
        else:
            print("❌ ملف نموذج استدعاء التلاميذ غير موجود")
            print("💡 سيتم إنشاؤه عند الضغط على الزر لأول مرة")
    else:
        print("❌ مجلد الجدولة غير موجود")
        print("💡 سيتم إنشاؤه تلقائياً عند فتح النافذة")
    
    return True

def test_window_with_new_button():
    """اختبار النافذة مع الزر الجديد"""
    try:
        from sub26_window import ExamScheduleInstructions
        
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء النافذة
        window = ExamScheduleInstructions()
        
        print("✅ تم إنشاء النافذة بنجاح")
        print(f"📐 حجم النافذة: {window.size().width()} x {window.size().height()}")
        print(f"📝 عنوان النافذة: {window.windowTitle()}")
        
        # عرض النافذة
        window.show()
        
        print("🚀 تم فتح نافذة تعليمات جدولة الامتحان بنجاح!")
        print("\nالأزرار المتاحة:")
        print("- عرض الجدولة الحالية")
        print("- استيراد جدولة الامتحان (PDF)")
        print("- فتح مجلد الجدولة")
        print("- فتح نموذج استدعاء التلاميذ ← (زر محدث)")
        print("- إغلاق")
        print("- 🔍 (اختبار قاعدة البيانات)")
        
        print("\n" + "="*50)
        print("💡 نصائح للاختبار:")
        print("1. اضغط على 'فتح نموذج استدعاء التلاميذ' لاختبار الوظيفة الجديدة")
        print("2. إذا لم يكن الملف موجوداً، اختر 'Yes' لإنشاء نموذج تلقائي")
        print("3. استخدم زر 🔍 لفحص قاعدة البيانات")
        print("4. جرب استيراد ملف PDF للجدولة")
        
        # تشغيل التطبيق
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل النافذة: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🧪 اختبار شامل لنافذة جدولة الامتحان مع الزر المحدث")
    print("=" * 60)
    
    # اختبار وظيفة نموذج Word
    test_word_template_functionality()
    
    print("=" * 60)
    
    # اختبار النافذة
    test_window_with_new_button()

if __name__ == "__main__":
    main()
