import os
import sys
import sqlite3
import traceback
from datetime import datetime
import subprocess

# ================= إعدادات خاصة بتقارير الغياب =================
# الجدول الأول: صفان، 4 أعمدة
COL_WIDTHS_TABLE1 = [70, 30, 60, 35]
# الجدول الثاني: جدول المترشحين الغائبين مع عمود المؤسسة الأصلية
COL_WIDTHS_TABLE2 = [60, 30, 30, 30, 30, 15]  # إزالة عمود غائب وتاريخ الازدياد، إضافة المؤسسة الأصلية
# عناوين جدول الغياب
TABLE2_HEADERS = ['المؤسسة الأصلية', 'القسم', 'الرقم الوطني', 'الاسم الكامل', 'رقم الامتحان', 'ر.ت']

# إضافة ارتفاعات الصفوف
ROW_HEIGHT_TABLE1 = 8  # ارتفاع صفوف الجدول الأول
ROW_HEIGHT_TABLE2 = 6   # ارتفاع صفوف الجدول الثاني (جدول المترشحين الغائبين)
ROW_HEIGHT_HEADER = 10  # ارتفاع صفوف الرأس
ROW_HEIGHT_TABLE_HEADER = 12  # ارتفاع صف رأس الجدول

# إعدادات الهوامش
PAGE_MARGIN_TOP = 0.2
PAGE_MARGIN_BOTTOM = 0.2
PAGE_MARGIN_LEFT = 10
PAGE_MARGIN_RIGHT = 10

PT_TO_MM = 0.3528
LOGO_W_PT, LOGO_H_PT = 200, 80
BOX1_W_PT, BOX2_W_PT = 380, 170
TITLE_H_PT = 40
LOGO_W = LOGO_W_PT * PT_TO_MM
LOGO_H = LOGO_H_PT * PT_TO_MM
BOX1_W = BOX1_W_PT * PT_TO_MM
BOX2_W = BOX2_W_PT * PT_TO_MM
BOX_H = TITLE_H_PT * PT_TO_MM

try:
    from fpdf import FPDF
    import arabic_reshaper
    from bidi.algorithm import get_display
except ImportError:
    subprocess.check_call([sys.executable, "-m", "pip", "install", "fpdf2", "arabic-reshaper", "python-bidi"])
    from fpdf import FPDF
    import arabic_reshaper
    from bidi.algorithm import get_display

class ArabicPDF(FPDF):
    def __init__(self):
        super().__init__('P','mm','A4')
        self.set_margins(PAGE_MARGIN_LEFT, PAGE_MARGIN_TOP, PAGE_MARGIN_RIGHT)
        self.set_auto_page_break(auto=True, margin=PAGE_MARGIN_BOTTOM)
        fonts_dir = os.path.join(os.path.dirname(__file__), 'fonts')
        self.add_font('Arial', '', os.path.join(fonts_dir, 'arial.ttf'))
        self.add_font('Arial', 'B', os.path.join(fonts_dir, 'arialbd.ttf'))
        self.set_font('Arial', '', 12)
        self.set_line_width(0.4)

    def ar_text(self, txt: str) -> str:
        """تحويل النص العربي ليتم عرضه بشكل صحيح"""
        reshaped = arabic_reshaper.reshape(str(txt))
        return get_display(reshaped)

def generate_absence_report(logo_path, records, output_path, absence_data):
    """إنشاء تقرير الغياب"""
    pdf = ArabicPDF()
    margin = 10
    usable_w = pdf.w - 2 * margin

    # ترتيب جميع السجلات حسب رقم الامتحان بدون تجميع حسب القاعة
    sorted_records = sorted(records, key=lambda x: int(x.get('رقم_الامتحان', '0')))

    # تحديد ارتفاع صفوف جدول المترشحين بناءً على عدد الصفوف
    global ROW_HEIGHT_TABLE2
    total_records = len(sorted_records)

    # تعديل ارتفاع الصفوف بناءً على عدد المترشحين الغائبين
    if total_records <= 15:
        ROW_HEIGHT_TABLE2 = 9
    elif 15 < total_records <= 20:
        ROW_HEIGHT_TABLE2 = 8
    elif 20 < total_records <= 25:
        ROW_HEIGHT_TABLE2 = 7
    else:
        ROW_HEIGHT_TABLE2 = 6

    pdf.add_page()
    y = pdf.get_y()
    
    # إضافة الشعار
    if logo_path:
        x_logo = (pdf.w - LOGO_W) / 2
        pdf.image(logo_path, x=x_logo, y=y, w=LOGO_W, h=LOGO_H)
    y += LOGO_H + 5

    # عنوان التقرير - محضر غياب المترشحين
    pdf.set_draw_color(220, 20, 60)  # لون أحمر للغياب
    pdf.set_line_width(0.5)
    FIXED_BOX_HEIGHT = 12

    x = margin
    # عنوان تقرير الغياب المحدث
    title_text = f"محضر غياب المترشحين - المادة : {absence_data.get('subject', 'المادة')} - {absence_data.get('period', 'الفترة')}"
    
    pdf.set_text_color(220, 20, 60)  # لون أحمر للنص

    # مربع العنوان يمتد على كامل العرض
    pdf.set_xy(x, y)
    pdf.set_font('Arial','B', 14)
    pdf.cell(usable_w, FIXED_BOX_HEIGHT, pdf.ar_text(title_text), border=1, align='C')

    pdf.set_text_color(0, 0, 0)  # إعادة اللون للأسود
    y += FIXED_BOX_HEIGHT + 5

    # الجدول الأول: معلومات الغياب
    cols1 = COL_WIDTHS_TABLE1
    
    # صفوف معلومات الغياب
    row1 = [absence_data.get('date', ''), 'تاريخ الغياب', absence_data.get('subject', ''), 'المادة']
    row2 = [str(len(sorted_records)), 'عدد الغائبين', absence_data.get('period', ''), 'الفترة']
    
    pdf.set_font('Arial','B',12)
    pdf.set_fill_color(255, 230, 230)  # خلفية وردية فاتحة
    
    # رسم الصف الأول
    x = margin
    for i, cell in enumerate(row1):
        pdf.set_xy(x, y)
        fill = i % 2 == 1
        align = 'C' if i % 2 == 1 else 'R'
        pdf.cell(cols1[i], ROW_HEIGHT_TABLE1, pdf.ar_text(cell), border=1, align=align, fill=fill)
        x += cols1[i]
        
    # رسم الصف الثاني
    y += ROW_HEIGHT_TABLE1
    x = margin
    for i, cell in enumerate(row2):
        pdf.set_xy(x, y)
        fill = i % 2 == 1
        align = 'C' if i % 2 == 1 else 'R'
        pdf.cell(cols1[i], ROW_HEIGHT_TABLE1, pdf.ar_text(cell), border=1, align=align, fill=fill)
        x += cols1[i]
        
    y += ROW_HEIGHT_TABLE1 + 5

    # الجدول الثاني: قائمة المترشحين الغائبين - جميع القاعات
    cols2 = COL_WIDTHS_TABLE2
    
    pdf.set_font('Arial','B',12)
    pdf.set_fill_color(255, 200, 200)  # خلفية حمراء فاتحة للرأس

    # رسم رأس الجدول
    x = margin
    for i, header in enumerate(TABLE2_HEADERS):
        pdf.set_xy(x, y)
        pdf.cell(cols2[i], ROW_HEIGHT_TABLE_HEADER, pdf.ar_text(header), border=1, align='C', fill=True)
        x += cols2[i]

    y += ROW_HEIGHT_TABLE_HEADER
    pdf.set_font('Arial','',11)

    # محتوى الجدول - جميع المترشحين الغائبين مرتبين حسب رقم الامتحان
    for i, rec in enumerate(sorted_records):
        x = margin

        # بيانات المترشح الغائب مع المؤسسة الأصلية
        data = [
            rec.get('المؤسسة_الأصلية',''),  # المؤسسة الأصلية
            rec.get('القسم',''), 
            rec.get('الرمز',''), 
            rec.get('الاسم_الكامل',''), 
            rec.get('رقم_الامتحان',''), 
            str(i+1)
        ]

        # تلوين صفوف الغائبين بلون وردي فاتح
        pdf.set_fill_color(255, 245, 245)

        # رسم خلايا الصف
        for j, cell in enumerate(data):
            pdf.set_xy(x, y)
            # تلوين عمود المؤسسة الأصلية بلون مميز
            if j == 0:  # عمود المؤسسة الأصلية
                pdf.set_fill_color(255, 235, 235)
                pdf.cell(cols2[j], ROW_HEIGHT_TABLE2, pdf.ar_text(cell), border=1, align='C', fill=True)
                pdf.set_fill_color(255, 245, 245)
            else:
                pdf.cell(cols2[j], ROW_HEIGHT_TABLE2, pdf.ar_text(cell), border=1, align='C', fill=True)
            x += cols2[j]

        y += ROW_HEIGHT_TABLE2

        # الانتقال إلى صفحة جديدة عند الحاجة
        if y > pdf.h - 50:  # ترك مساحة أكبر للتوقيع
            pdf.add_page()
            y = pdf.get_y()

    # إضافة ملاحظات الغياب في النهاية
    if absence_data.get('notes'):
        y += 10
        pdf.set_font('Arial','B',10)
        pdf.set_xy(margin, y)
        pdf.cell(0, 8, pdf.ar_text(f"ملاحظات: {absence_data['notes']}"), border=1, align='R')
        y += 15

    # إضافة خاتم وتوقيع رئيس المركز في النهاية
    y += 20
    signature_width = usable_w / 2  # تقسيم إلى نصفين
    pdf.set_font('Arial','B',12)
    
    # إضافة مساحة للخاتم والتوقيع في المنتصف
    x = margin + usable_w / 4  # وضع في المنتصف
    
    # مربع الخاتم والتوقيع
    pdf.set_xy(x, y)
    pdf.cell(signature_width, 20, '', border=1, align='C')
    
    # النص أسفل المربع
    pdf.set_xy(x, y + 22)
    pdf.cell(signature_width, 8, pdf.ar_text('خاتم وتوقيع السيد(ة) رئيس(ة) المركز'), border=0, align='C')

    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    pdf.output(output_path)
    print(f"تم إنشاء تقرير الغياب: {output_path}")

def print_absence_report_single(parent=None, selected_records=None, absence_data=None):
    """
    دالة لإنشاء تقرير الغياب من النافذة المجمعة
    
    المعاملات:
        parent: كائن النافذة الأم (لعرض رسائل)
        selected_records: قائمة السجلات المحددة للغياب
        absence_data: بيانات الغياب (التاريخ، المادة، الفترة، إلخ)

    العوائد:
        (success, output_path, message): ثلاثية تحدد نجاح العملية ومسار الملف ورسالة النتيجة
    """
    try:
        if not selected_records:
            return False, None, "لا توجد سجلات محددة للغياب."

        if not absence_data:
            return False, None, "بيانات الغياب غير متوفرة."

        # تحديد مسار قاعدة البيانات
        db_path = os.path.join(os.path.dirname(__file__), 'data.db')
        
        # جلب بيانات كاملة للمترشحين من قاعدة البيانات
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # جلب شعار المؤسسة
            cursor.execute("SELECT ImagePath1 FROM بيانات_المؤسسة LIMIT 1")
            logo_row = cursor.fetchone()
            logo_path = logo_row[0] if logo_row and os.path.exists(logo_row[0]) else None
            
            # بناء البيانات الكاملة للمترشحين
            complete_records = []
            for record in selected_records:
                exam_number = record.get('رقم_الامتحان', '')
                name = record.get('الاسم_الكامل', '')
                
                if exam_number:
                    # البحث عن بيانات المترشح في جدول امتحانات
                    cursor.execute("""
                        SELECT رقم_الامتحان, الاسم_الكامل, الرمز, القسم, المؤسسة_الأصلية
                        FROM امتحانات 
                        WHERE رقم_الامتحان = ?
                    """, (exam_number,))
                    
                    result = cursor.fetchone()
                    if result:
                        complete_records.append({
                            'رقم_الامتحان': result[0] or '',
                            'الاسم_الكامل': result[1] or '',
                            'الرمز': result[2] or '',
                            'القسم': result[3] or '',
                            'المؤسسة_الأصلية': result[4] or '',
                            'القاعة': '',
                            'تاريخ_الازدياد': ''
                        })
                    else:
                        # إذا لم يتم العثور على البيانات، استخدم البيانات المتوفرة
                        complete_records.append({
                            'رقم_الامتحان': exam_number,
                            'الاسم_الكامل': name,
                            'الرمز': '',
                            'القسم': '',
                            'المؤسسة_الأصلية': '',
                            'القاعة': '',
                            'تاريخ_الازدياد': ''
                        })
                else:
                    # إذا لم يكن رقم الامتحان متوفراً، استخدم البيانات المتوفرة
                    complete_records.append({
                        'رقم_الامتحان': exam_number,
                        'الاسم_الكامل': name,
                        'الرمز': '',
                        'القسم': '',
                        'المؤسسة_الأصلية': '',
                        'القاعة': '',
                        'تاريخ_الازدياد': ''
                    })
            
            conn.close()
            
        except Exception as db_error:
            print(f"خطأ في الوصول لقاعدة البيانات: {db_error}")
            # في حالة فشل الوصول لقاعدة البيانات، استخدم البيانات المتوفرة
            complete_records = selected_records
            logo_path = None

        # إنشاء مجلد تقارير الغياب إذا لم يكن موجوداً
        reports_dir = os.path.join(os.path.expanduser('~'), 'Desktop', 'تقارير الغياب المجمعة')
        if not os.path.exists(reports_dir):
            os.makedirs(reports_dir)

        # تحديد اسم الملف
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        subject_name = absence_data.get('subject', 'مادة').replace(' ', '_').replace('/', '_')
        date_str = absence_data.get('date', '').replace('-', '_')
        output_path = os.path.join(reports_dir, f"تقرير_غياب_مجمع_{subject_name}_{date_str}_{timestamp}.pdf")

        # إنشاء تقرير الغياب
        generate_absence_report(logo_path, complete_records, output_path, absence_data)

        # فتح الملف بعد إنشائه
        try:
            if sys.platform == 'win32':
                os.startfile(output_path)
            elif sys.platform == 'darwin':  # macOS
                subprocess.call(['open', output_path])
            else:  # Linux
                subprocess.call(['xdg-open', output_path])
        except Exception as e:
            return True, output_path, f"تم إنشاء تقرير الغياب ولكن تعذر فتح الملف: {str(e)}"

        return True, output_path, "تم إنشاء تقرير الغياب المجمع بنجاح."
        
    except Exception as e:
        traceback.print_exc()
        return False, None, f"حدث خطأ في إنشاء تقرير الغياب المجمع: {str(e)}"

def print_absence_report(parent=None, selected_records=None, absence_data=None):
    """
    دالة لإنشاء تقرير الغياب، يمكن استدعاؤها من واجهات PyQt5

    المعاملات:
        parent: كائن النافذة الأم (لعرض رسائل)
        selected_records: قائمة السجلات المحددة للغياب
        absence_data: بيانات الغياب (التاريخ، المادة، الفترة، إلخ)

    العوائد:
        (success, output_path, message): ثلاثية تحدد نجاح العملية ومسار الملف ورسالة النتيجة
    """
    try:
        if not selected_records:
            return False, None, "لا توجد سجلات محددة للغياب."

        if not absence_data:
            return False, None, "بيانات الغياب غير متوفرة."

        # تحديد مسار قاعدة البيانات
        db_path = os.path.join(os.path.dirname(__file__), 'data.db')
        
        # جلب شعار المؤسسة
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT ImagePath1 FROM بيانات_المؤسسة LIMIT 1")
        logo_row = cursor.fetchone()
        logo_path = logo_row[0] if logo_row and os.path.exists(logo_row[0]) else None
        conn.close()

        # إنشاء مجلد تقارير الغياب إذا لم يكن موجوداً
        reports_dir = os.path.join(os.path.expanduser('~'), 'Desktop', 'تقارير الغياب')
        if not os.path.exists(reports_dir):
            os.makedirs(reports_dir)

        # تحديد اسم الملف
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        subject_name = absence_data.get('subject', 'مادة').replace(' ', '_').replace('/', '_')
        date_str = absence_data.get('date', '').replace('-', '_')
        output_path = os.path.join(reports_dir, f"تقرير_غياب_{subject_name}_{date_str}_{timestamp}.pdf")

        # إنشاء تقرير الغياب
        generate_absence_report(logo_path, selected_records, output_path, absence_data)

        # فتح الملف بعد إنشائه
        try:
            if sys.platform == 'win32':
                os.startfile(output_path)
            elif sys.platform == 'darwin':  # macOS
                subprocess.call(['open', output_path])
            else:  # Linux
                subprocess.call(['xdg-open', output_path])
        except Exception as e:
            return True, output_path, f"تم إنشاء تقرير الغياب ولكن تعذر فتح الملف: {str(e)}"

        return True, output_path, "تم إنشاء تقرير الغياب بنجاح."
        
    except Exception as e:
        traceback.print_exc()
        return False, None, f"حدث خطأ في إنشاء تقرير الغياب: {str(e)}"

if __name__=='__main__':
    # مثال على الاستخدام
    try:
        # بيانات تجريبية للاختبار
        test_records = [
            {
                'رقم_الامتحان': '1001',
                'الاسم_الكامل': 'أحمد محمد علي',
                'الرمز': 'CD12345',
                'القسم': '3ASCG-1',
                'القاعة': '1',
                'تاريخ_الازدياد': '1995-05-15'
            },
            {
                'رقم_الامتحان': '1002',
                'الاسم_الكامل': 'فاطمة أحمد محمد',
                'الرمز': 'CD12346',
                'القسم': '3ASCG-1',
                'القاعة': '1',
                'تاريخ_الازدياد': '1996-03-20'
            }
        ]
        
        test_absence_data = {
            'subject': 'الرياضيات',
            'date': '2024-12-20',
            'period': 'الفترة الصباحية',
            'notes': 'غياب بدون عذر'
        }

        success, output_path, message = print_absence_report(
            selected_records=test_records,
            absence_data=test_absence_data
        )
        
        print(f"النتيجة: {success}")
        print(f"المسار: {output_path}")
        print(f"الرسالة: {message}")
        
    except Exception as e:
        print(f"خطأ في الاختبار: {e}")
        traceback.print_exc()
