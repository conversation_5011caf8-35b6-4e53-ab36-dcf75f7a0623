"""
نافذة إدارة التقارير واللوائح والاستدعاءات
"""

import os
import sys
import sqlite3
import time
import subprocess
import io
from pathlib import Path
from datetime import datetime
from PyQt5.QtWidgets import (
    QDialog, QApplication, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QLineEdit, QPushButton, QMessageBox, QFrame, QTextEdit,
    QProgressDialog, QComboBox, QDialogButtonBox, QFileDialog
)
from PyQt5.QtGui import QFont, QIcon, QPixmap
from PyQt5.QtCore import Qt, QSize

class CustomMessageDialog(QDialog):
    """نافذة رسائل مخصصة بنفس نمط sub100_window.py"""

    def __init__(self, parent=None, title="", message="", icon_type="info"):
        super().__init__(parent)
        self.setWindowTitle(title)

        # تعديل حجم النافذة حسب نوع الرسالة
        if icon_type == "success":
            self.setFixedSize(450, 350)  # حجم نافذة النجاح 450×350
        else:
            self.setFixedSize(450, 250)  # حجم النوافذ الأخرى 450×250

        self.setLayoutDirection(Qt.RightToLeft)

        # إضافة أيقونة البرنامج
        try:
            app_icon = QIcon("01.ico")
            self.setWindowIcon(app_icon)

            # تعيين أيقونة البرنامج كأيقونة للنافذة
            if parent and hasattr(parent, 'windowIcon') and not parent.windowIcon().isNull():
                self.setWindowIcon(parent.windowIcon())
        except Exception as e:
            print(f"خطأ في تحميل أيقونة البرنامج: {e}")

        # تحديد ألوان النافذة حسب نوع الرسالة
        if icon_type == "warning":
            bg_color = "#fff8f0"
            border_color = "#f39c12"
            title_color = "#d35400"
            button_bg = "#f39c12"
            button_hover_bg = "#e67e22"
        elif icon_type == "error":
            bg_color = "#fff0f0"
            border_color = "#e74c3c"
            title_color = "#c0392b"
            button_bg = "#e74c3c"
            button_hover_bg = "#c0392b"
        elif icon_type == "success":
            bg_color = "#f0fff0"
            border_color = "#2ecc71"
            title_color = "#27ae60"
            button_bg = "#2ecc71"
            button_hover_bg = "#27ae60"
        else:  # info
            bg_color = "#f0f8ff"
            border_color = "#3498db"
            title_color = "#2980b9"
            button_bg = "#3498db"
            button_hover_bg = "#2980b9"

        # تنسيق النافذة
        self.setStyleSheet(f"""
            QDialog {{
                background-color: {bg_color};
                border: 2px solid {border_color};
                border-radius: 10px;
            }}
            QLabel {{
                color: #333333;
                font-weight: bold;
            }}
            QLabel#message_label {{
                background-color: white;
                border: 1px solid {border_color};
                border-radius: 5px;
                padding: 15px;
                font-size: 14pt;
            }}
            QPushButton {{
                background-color: {button_bg};
                color: white;
                border-radius: 5px;
                padding: 8px 15px;
                font-weight: bold;
                min-height: 35px;
                min-width: 120px;
            }}
            QPushButton:hover {{
                background-color: {button_hover_bg};
                border: 2px solid {button_bg};
            }}
        """)

        # إنشاء تخطيط النافذة
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # إضافة أيقونة وعنوان
        header_layout = QHBoxLayout()

        # محاولة إضافة أيقونة مناسبة
        icon_label = QLabel()
        icon_label.setAlignment(Qt.AlignCenter)
        try:
            # استخدام أيقونة قياسية
            if icon_type == "warning":
                icon = QPixmap(self.style().standardPixmap(self.style().SP_MessageBoxWarning))
            elif icon_type == "error":
                icon = QPixmap(self.style().standardPixmap(self.style().SP_MessageBoxCritical))
            elif icon_type == "success":
                icon = QPixmap(self.style().standardPixmap(self.style().SP_MessageBoxInformation))
            else:  # info
                icon = QPixmap(self.style().standardPixmap(self.style().SP_MessageBoxInformation))

            icon = icon.scaled(48, 48, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            icon_label.setPixmap(icon)
        except Exception:
            # استخدام نص بديل في حالة الفشل
            if icon_type == "warning":
                icon_label.setText("⚠️")
            elif icon_type == "error":
                icon_label.setText("❌")
            elif icon_type == "success":
                icon_label.setText("✓")
            else:  # info
                icon_label.setText("ℹ️")
            icon_label.setFont(QFont("Arial", 24))
            icon_label.setStyleSheet(f"color: {border_color};")

        header_layout.addWidget(icon_label)

        # إضافة عنوان النافذة
        title_label = QLabel(title)
        title_label.setFont(QFont("Calibri", 16, QFont.Bold))
        title_label.setStyleSheet(f"color: {title_color};")
        title_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(title_label, 1)

        layout.addLayout(header_layout)

        # إضافة رسالة
        message_label = QLabel(message)
        message_label.setObjectName("message_label")

        # تعديل خط الرسالة حسب نوع الرسالة
        if icon_type == "success":
            message_label.setFont(QFont("Calibri", 13, QFont.Bold))
            message_label.setStyleSheet("color: #000080;")  # أزرق غامق
        else:
            message_label.setFont(QFont("Calibri", 13))

        message_label.setAlignment(Qt.AlignCenter)
        message_label.setWordWrap(True)
        layout.addWidget(message_label)

        # إضافة زر الموافقة
        button_layout = QHBoxLayout()
        ok_button = QPushButton("موافق")
        ok_button.setFont(QFont("Calibri", 12, QFont.Bold))
        ok_button.setCursor(Qt.PointingHandCursor)
        ok_button.clicked.connect(self.accept)
        button_layout.addWidget(ok_button)

        layout.addLayout(button_layout)

# دالة مساعدة للحصول على مجلد التنزيلات
def get_downloads_folder():
    """الحصول على مسار مجلد التنزيلات"""
    # محاولة الحصول على مجلد التنزيلات بطريقة متوافقة مع أنظمة التشغيل المختلفة
    try:
        # في ويندوز
        if os.name == 'nt':
            import winreg
            sub_key = r'SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\Shell Folders'
            downloads_guid = '{374DE290-123F-4565-9164-39C4925E467B}'
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, sub_key) as key:
                downloads_folder = winreg.QueryValueEx(key, downloads_guid)[0]
                return downloads_folder

        # في ماك وأنظمة يونكس
        else:
            downloads_folder = os.path.join(os.path.expanduser('~'), 'Downloads')
            if os.path.exists(downloads_folder):
                return downloads_folder
    except Exception:
        pass

    # إذا فشلت الطرق السابقة، استخدم مجلد المستندات
    try:
        documents_folder = os.path.join(os.path.expanduser('~'), 'Documents')
        if os.path.exists(documents_folder):
            downloads_folder = os.path.join(documents_folder, 'Downloads')
            os.makedirs(downloads_folder, exist_ok=True)
            return downloads_folder
    except Exception:
        pass

    # إذا فشلت جميع المحاولات، استخدم سطح المكتب
    desktop_folder = os.path.join(os.path.expanduser('~'), 'Desktop')
    downloads_folder = os.path.join(desktop_folder, 'Downloads')
    os.makedirs(downloads_folder, exist_ok=True)
    return downloads_folder

class Sub40Window(QDialog):
    """نافذة إدارة التقارير واللوائح والاستدعاءات"""

    def __init__(self, parent=None, db_path=None):
        super().__init__(parent)
        self.parent = parent
        self.db_path = db_path or "data.db"

        self.initUI()
        self.loadData()

    def initUI(self):
        """إعداد واجهة المستخدم"""
        # إعداد النافذة الرئيسية
        self.setWindowTitle("إدارة التقارير واللوائح والاستدعاءات")
        self.setFixedSize(1000, 800)
        self.setLayoutDirection(Qt.RightToLeft)

        # إضافة أيقونة البرنامج
        try:
            app_icon = QIcon("01.ico")
            self.setWindowIcon(app_icon)
        except Exception as e:
            print(f"خطأ في تحميل أيقونة البرنامج: {e}")

        # إعداد التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)

        # إضافة عنوان النافذة
        title_label = QLabel("إدارة التقارير واللوائح والاستدعاءات")
        title_label.setFont(QFont("Calibri", 20, QFont.Bold))
        title_label.setStyleSheet("color: #2980b9;")
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)

        # إضافة عنوان فرعي ثابت
        subtitle_label = QLabel("يمكن للمستخدم تغيير وتبديل عناوين التقارير حسب رغبته بشرط أن لا يتجاوز الإطار الخاص بالعنوان")
        subtitle_label.setFont(QFont("Calibri", 12, QFont.Bold))
        subtitle_label.setStyleSheet("color: #27ae60;")
        subtitle_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(subtitle_label)

        # إضافة مسافة بعد العنوان الفرعي
        main_layout.addSpacing(10)

        # إضافة إطار لمربعات النص
        form_frame = QFrame()
        form_frame.setFrameShape(QFrame.StyledPanel)
        form_frame.setStyleSheet("""
            QFrame {
                background-color: #f0f8ff;
                border: 1px solid #3498db;
                border-radius: 10px;
            }
        """)

        form_layout = QVBoxLayout(form_frame)
        form_layout.setContentsMargins(20, 20, 20, 20)
        form_layout.setSpacing(15)

        # إضافة مربعات النص مع تسمياتها
        self.text_fields = {}
        field_names = [
            ("العنوان1", "عنوان الاستدعاء:"),
            ("العنوان2", "عنوان لائحة الحضور:"),
            ("العنوان3", "عنوان الإحصائيات:"),
            ("العنوان4", "عنوان جدولة الامتحان:"),
            ("العنوان5", "عنوان: المحضر الجماعي")
        ]

        for field_id, field_label in field_names:
            field_layout = QHBoxLayout()

            label = QLabel(field_label)
            label.setFont(QFont("Calibri", 14, QFont.Bold))
            label.setStyleSheet("color: #2980b9; border: 1px solid #3498db; border-radius: 5px; padding: 5px;")
            label.setFixedWidth(200)
            label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)

            text_field = QLineEdit()
            text_field.setFont(QFont("Calibri", 14))
            text_field.setStyleSheet("""
                QLineEdit {
                    border: 1px solid #3498db;
                    border-radius: 5px;
                    padding: 5px;
                    background-color: white;
                    color: black;
                    font-weight: bold;
                }
            """)
            text_field.setFixedSize(600, 40)

            self.text_fields[field_id] = text_field

            field_layout.addWidget(label)
            field_layout.addWidget(text_field)
            field_layout.addStretch()

            form_layout.addLayout(field_layout)

        main_layout.addWidget(form_frame)

        # إضافة إطار للأزرار
        buttons_frame = QFrame()
        buttons_frame.setFrameShape(QFrame.StyledPanel)
        buttons_frame.setStyleSheet("""
            QFrame {
                background-color: #f0f8ff;
                border: 1px solid #3498db;
                border-radius: 10px;
            }
        """)

        buttons_layout = QVBoxLayout(buttons_frame)
        buttons_layout.setContentsMargins(20, 20, 20, 20)
        buttons_layout.setSpacing(15)

        # إضافة الأزرار في صفين
        button_grid = QGridLayout()
        button_grid.setSpacing(10)

        button_data = [
            ("لوائح الحضور", self.attendance_by_subject, 0, 0, "#2ecc71"),
            ("استدعاءات المترشحين", self.candidate_calls, 0, 1, "#3498db"),
            ("إحصائيات", self.statistics, 0, 2, "#e74c3c"),
            ("لوائح المترشحين", self.attendance_all_subjects, 0, 3, "#f39c12"),
            ("ملصقات الطاولات", self.table_labels, 1, 0, "#9b59b6"),
            ("توجيهات عامة للمترشح (ة)", self.general_instructions, 1, 1, "#95a5a6"),
            ("المحضر الجماعي للمترشحين", self.collective_report, 1, 2, "#1abc9c"),
            ("إضافة خلفية", self.add_background, 1, 3, "#e67e22"),
            ("حفظ", self.save_changes, 2, 0, "#1abc9c")
        ]

        for button_text, button_function, row, col, button_color in button_data:
            button = QPushButton(button_text)
            button.setFont(QFont("Calibri", 12, QFont.Bold))
            button.setStyleSheet(f"""
                QPushButton {{
                    background-color: {button_color};
                    color: white;
                    border-radius: 5px;
                    padding: 5px;
                }}
                QPushButton:hover {{
                    background-color: {button_color}99;
                    border: 2px solid {button_color};
                }}
            """)
            button.setFixedSize(220, 35)  # تعديل الارتفاع من 30 إلى 35
            button.setCursor(Qt.PointingHandCursor)
            button.clicked.connect(button_function)

            button_grid.addWidget(button, row, col, Qt.AlignCenter)

        buttons_layout.addLayout(button_grid)
        main_layout.addWidget(buttons_frame)

        # إضافة مساحة فارغة في النهاية
        main_layout.addStretch()

    def loadData(self):
        """تحميل البيانات من قاعدة البيانات"""
        try:
            # التحقق من وجود جدول الامتحان وإنشائه إذا لم يكن موجودًا
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # التحقق من وجود الجدول
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='جدول_الامتحان'")
            if not cursor.fetchone():
                # إنشاء الجدول إذا لم يكن موجودًا
                cursor.execute("""
                    CREATE TABLE جدول_الامتحان (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        العنوان1 TEXT,
                        العنوان2 TEXT,
                        العنوان3 TEXT,
                        العنوان4 TEXT,
                        العنوان5 TEXT,
                        مسار_خلفية_PDF TEXT
                    )
                """)
                # إضافة سجل فارغ
                cursor.execute("""
                    INSERT INTO جدول_الامتحان (العنوان1, العنوان2, العنوان3, العنوان4, العنوان5, مسار_خلفية_PDF)
                    VALUES ('', '', '', '', '', '')
                """)
                conn.commit()
                self.show_message("معلومات", "تم إنشاء جدول الامتحان بنجاح.", "info")
            else:
                # التحقق من وجود عمود مسار_خلفية_PDF وإضافته إذا لم يكن موجودًا
                cursor.execute("PRAGMA table_info(جدول_الامتحان)")
                columns = [column[1] for column in cursor.fetchall()]
                if 'مسار_خلفية_PDF' not in columns:
                    cursor.execute("ALTER TABLE جدول_الامتحان ADD COLUMN مسار_خلفية_PDF TEXT")
                    conn.commit()

            # التحقق من وجود سجلات في الجدول
            cursor.execute("SELECT COUNT(*) FROM جدول_الامتحان")
            count = cursor.fetchone()[0]

            if count == 0:
                # إضافة سجل فارغ إذا كان الجدول فارغًا
                cursor.execute("""
                    INSERT INTO جدول_الامتحان (العنوان1, العنوان2, العنوان3, العنوان4, العنوان5)
                    VALUES ('', '', '', '', '')
                """)
                conn.commit()

            # تحميل السجل الأول
            cursor.execute("SELECT * FROM جدول_الامتحان LIMIT 1")
            record = cursor.fetchone()

            if record:
                # عرض البيانات في مربعات النص
                self.text_fields["العنوان1"].setText(record[1] or "")
                self.text_fields["العنوان2"].setText(record[2] or "")
                self.text_fields["العنوان3"].setText(record[3] or "")
                self.text_fields["العنوان4"].setText(record[4] or "")
                self.text_fields["العنوان5"].setText(record[5] or "")

            # تحميل مسار خلفية PDF من جدول بيانات_المؤسسة
            try:
                print("🔍 جاري تحميل مسار الخلفية من جدول بيانات_المؤسسة...")
                cursor.execute("SELECT ImagePath2 FROM بيانات_المؤسسة LIMIT 1")
                bg_record = cursor.fetchone()
                
                if bg_record and bg_record[0]:
                    self.background_pdf_path = bg_record[0]
                    print(f"✅ تم تحميل مسار الخلفية: {self.background_pdf_path}")
                    
                    # التحقق من وجود الملف
                    if not os.path.exists(self.background_pdf_path):
                        print(f"⚠️ ملف الخلفية غير موجود: {self.background_pdf_path}")
                        self.background_pdf_path = ""
                else:
                    self.background_pdf_path = ""
                    print("ℹ️ لا توجد خلفية محفوظة")
            except Exception as bg_error:
                print(f"⚠️ خطأ في تحميل مسار الخلفية: {bg_error}")
                self.background_pdf_path = ""

            conn.close()
        except Exception as e:
            self.show_message("خطأ", f"حدث خطأ أثناء تحميل البيانات: {str(e)}", "error")

    def show_message(self, title, message, icon_type="info"):
        """عرض رسالة مخصصة"""
        dialog = CustomMessageDialog(self, title, message, icon_type)
        dialog.exec_()

    def attendance_by_subject(self):
        """إنشاء لوائح الحضور"""
        try:
            # تعيين عنوان النافذة
            self.setWindowTitle("لوائح الحضور - إدارة التقارير واللوائح والاستدعاءات")

            # التحقق من إدخال البيانات المطلوبة
            title2 = self.text_fields["العنوان2"].text().strip()

            if not title2:
                self.show_message("تنبيه", "الرجاء إدخال عنوان لائحة الحضور على الأقل.", "warning")
                return

            # إنشاء التقرير مباشرة بدون اختيار المادة (ملء يدوي)
            subject_data = None  # بدون مواد (ملء يدوي)

            # محاولة استدعاء دالة إنشاء محضر التوقيعات من ملف print11.py
            try:
                # إنشاء شريط تقدم العملية محسن
                progress = QProgressDialog("جاري إنشاء لائحة الحضور...", "إلغاء", 0, 100, self)
                progress.setWindowTitle("إنشاء لائحة الحضور")
                progress.setWindowModality(Qt.WindowModal)
                progress.setMinimumDuration(0)
                progress.setAutoClose(True)
                progress.setAutoReset(True)
                progress.setMinimumWidth(400)

                # تحسين مظهر شريط التقدم
                progress.setStyleSheet("""
                    QProgressDialog {
                        background-color: #f0f8ff;
                        border: 2px solid #3498db;
                        border-radius: 10px;
                        padding: 10px;
                    }
                    QLabel {
                        color: #2980b9;
                        font-family: Calibri;
                        font-size: 12pt;
                        font-weight: bold;
                    }
                    QProgressBar {
                        border: 1px solid #3498db;
                        border-radius: 5px;
                        background-color: #ecf0f1;
                        text-align: center;
                        color: black;
                        font-family: Calibri;
                        font-weight: bold;
                        min-height: 20px;
                    }
                    QProgressBar::chunk {
                        background-color: #3498db;
                        width: 10px;
                        margin: 0.5px;
                    }
                    QPushButton {
                        background-color: #e74c3c;
                        color: white;
                        border-radius: 5px;
                        padding: 5px 10px;
                        font-family: Calibri;
                        font-weight: bold;
                    }
                    QPushButton:hover {
                        background-color: #c0392b;
                    }
                """)

                progress.setValue(0)
                progress.show()

                # استيراد وحدة time للتأخير
                import time

                # تحديث شريط التقدم - بدء العملية
                progress.setLabelText("جاري تهيئة العملية...")
                progress.setValue(10)
                time.sleep(0.3)  # إضافة تأخير قصير لعرض التقدم

                # استيراد ملف print11.py
                import print11

                # تحديث شريط التقدم - جلب البيانات
                progress.setLabelText("جاري جلب بيانات المترشحين...")
                progress.setValue(30)
                time.sleep(0.3)

                # تحديث شريط التقدم - إنشاء التقرير
                progress.setLabelText("جاري إنشاء ملف PDF...")
                progress.setValue(50)

                # استدعاء دالة إنشاء التقرير بدون بيانات مادة محددة (ملء يدوي)
                success, output_path, message = print11.print_exams_report(
                    parent=self,
                    report_title=title2,
                    subject_data=subject_data
                )

                # التأكد من إنشاء التقرير بنجاح قبل تطبيق الخلفية
                if success and output_path:
                    print("✅ تم إنشاء تقرير لوائح الحضور الأساسي بنجاح")
                    print(f"📁 مسار التقرير: {output_path}")
                    
                    # تأخير قصير للتأكد من حفظ الملف بالكامل
                    progress.setLabelText("⏳ جاري الانتظار لضمان حفظ التقرير...")
                    progress.setValue(60)
                    QApplication.processEvents()
                    time.sleep(1)  # تأخير ثانية واحدة
                    
                    # التحقق من وجود الملف فعلياً
                    if os.path.exists(output_path):
                        print(f"✅ تم التأكد من وجود الملف: {output_path}")
                        
                        # التحقق من صحة ملف التقرير
                        if self.verify_report_file(output_path):
                            print("✅ ملف التقرير صالح ومقروء")
                            
                            # محاولة تطبيق الخلفية المحفوظة من جدول بيانات_المؤسسة
                            print("🎨 بدء البحث عن خلفية محفوظة لتطبيقها على لوائح الحضور...")
                            
                            # البحث عن الخلفية المحفوظة
                            background_path = self.get_background_path()
                            background_applied = False
                            
                            if background_path:
                                print(f"🔍 تم العثور على مسار خلفية: {background_path}")
                                
                                # اختبار الخلفية أولاً
                                if self.test_background_before_applying(background_path):
                                    # تحديث شريط التقدم - تطبيق الخلفية
                                    progress.setLabelText("🎨 جاري تطبيق الخلفية المحسنة على لوائح الحضور...")
                                    progress.setValue(75)
                                    QApplication.processEvents()
                                    time.sleep(0.3)
                                    
                                    print(f"📁 بدء تطبيق الخلفية من: {background_path}")
                                    
                                    try:
                                        # تأكد من أن مسار الخلفية محفوظ في الذاكرة
                                        self.background_pdf_path = background_path
                                        
                                        # تطبيق الخلفية باستخدام الطريقة المحسنة المُحدثة
                                        background_applied = self.apply_background_to_existing_pdf(output_path, background_path)
                                        
                                        if background_applied:
                                            print("🎉 تم تطبيق الخلفية على لوائح الحضور بنجاح!")
                                            
                                            # التحقق من النتيجة النهائية
                                            if os.path.exists(output_path):
                                                file_size = os.path.getsize(output_path)
                                                print(f"📊 حجم الملف النهائي: {file_size} بايت")
                                            else:
                                                print("⚠️ الملف النهائي غير موجود!")
                                        else:
                                            print("⚠️ فشل في تطبيق الخلفية على لوائح الحضور")
                                            
                                    except Exception as bg_error:
                                        print(f"❌ خطأ في تطبيق الخلفية على لوائح الحضور: {bg_error}")
                                        import traceback
                                        traceback.print_exc()
                                        background_applied = False
                                else:
                                    print("❌ اختبار الخلفية فشل - لن يتم تطبيقها")
                            else:
                                print("ℹ️ لا توجد خلفية محفوظة - سيتم إنشاء لوائح الحضور بدون خلفية")
                            
                            # تحديث شريط التقدم - التحقق من سلامة الملف النهائي
                            progress.setLabelText("🔍 جاري التحقق من سلامة الملف النهائي...")
                            progress.setValue(90)
                            QApplication.processEvents()
                            time.sleep(0.3)
                            
                            # التحقق من سلامة الملف النهائي قبل فتحه
                            final_file_valid = False
                            try:
                                if self.verify_report_file(output_path):
                                    print("✅ الملف النهائي صالح وجاهز للفتح")
                                    final_file_valid = True
                                else:
                                    print("❌ الملف النهائي تالف أو غير قابل للقراءة")
                            except Exception as verify_error:
                                print(f"⚠️ خطأ في التحقق من الملف النهائي: {verify_error}")
                            
                            # فتح الملف النهائي فقط إذا كان صالحاً
                            if final_file_valid:
                                progress.setLabelText("📂 جاري فتح لوائح الحضور...")
                                progress.setValue(95)
                                QApplication.processEvents()
                                
                                try:
                                    if sys.platform == 'win32':
                                        os.startfile(output_path)
                                    elif sys.platform == 'darwin':  # macOS
                                        subprocess.call(['open', output_path])
                                    else:  # Linux
                                        subprocess.call(['xdg-open', output_path])
                                    
                                    if background_applied:
                                        print("🎉 تم فتح لوائح الحضور مع الخلفية بنجاح!")
                                    else:
                                        print("📄 تم فتح لوائح الحضور بدون خلفية")
                                        
                                except Exception as open_error:
                                    print(f"⚠️ تعذر فتح الملف: {open_error}")
                                    self.show_message("تنبيه", f"تم إنشاء التقرير ولكن تعذر فتح الملف: {str(open_error)}", "warning")
                            else:
                                print("❌ لن يتم فتح الملف لأنه تالف")
                                self.show_message("خطأ", "تم إنشاء التقرير ولكنه تالف. يرجى المحاولة مرة أخرى بدون خلفية.", "error")
                                
                        else:
                            print("❌ ملف التقرير تالف أو غير مقروء - لن يتم تطبيق الخلفية")
                    else:
                        print(f"❌ لم يتم العثور على الملف المُنشأ: {output_path}")
                else:
                    print(f"❌ فشل في إنشاء التقرير: {message}")

                # تحديث شريط التقدم - اكتمال العملية
                progress.setLabelText("🎉 اكتملت العملية بنجاح!")
                progress.setValue(100)
                QApplication.processEvents()  # معالجة الأحداث المعلقة
                time.sleep(0.5)

                if success:
                    # تحديد رسالة النجاح بناءً على ما تم تطبيقه
                    success_message = "تم إنشاء لوائح الحضور بنجاح"
                    
                    # إضافة معلومة عن الخلفية إذا كانت متاحة
                    background_path = self.get_background_path()
                    if background_path:
                        success_message += " مع تطبيق الخلفية المحفوظة"
                    else:
                        success_message += " (بدون خلفية - لم يتم تحديد خلفية مسبقاً)"
                    
                    success_message += "."
                    self.show_message("نجاح", success_message, "success")
                else:
                    self.show_message("تنبيه", f"لم يتم إنشاء لوائح الحضور: {message}", "warning")
            except ImportError:
                self.show_message("خطأ", "لم يتم العثور على ملف print11.py اللازم لإنشاء لائحة الحضور.", "error")
            except Exception as e:
                self.show_message("خطأ", f"حدث خطأ أثناء إنشاء لائحة الحضور: {str(e)}", "error")
        except Exception as e:
            self.show_message("خطأ", f"حدث خطأ أثناء إنشاء لائحة الحضور: {str(e)}", "error")
        finally:
            # إعادة عنوان النافذة الأصلي
            self.setWindowTitle("إدارة التقارير واللوائح والاستدعاءات")

    def candidate_calls(self):
        """إنشاء استدعاءات المترشحين"""
        try:
            # تعيين عنوان النافذة
            self.setWindowTitle("استدعاءات المترشحين - إدارة التقارير واللوائح والاستدعاءات")

            # التحقق من إدخال البيانات المطلوبة
            title1 = self.text_fields["العنوان1"].text().strip()

            if not title1:
                self.show_message("تنبيه", "الرجاء إدخال عنوان الاستدعاء على الأقل.", "warning")
                return

            # إنشاء نافذة حوار للاختيار بين طباعة جميع الاستدعاءات أو حسب المؤسسة الأصلية
            choice_dialog = QDialog(self)
            choice_dialog.setWindowTitle("اختيار طريقة الطباعة")
            choice_dialog.setFixedSize(500, 300)  # حجم أكبر قليلاً للتنسيق الأفضل
            choice_dialog.setLayoutDirection(Qt.RightToLeft)
            
            # إضافة أيقونة البرنامج
            try:
                app_icon = QIcon("01.ico")
                choice_dialog.setWindowIcon(app_icon)
            except Exception as e:
                print(f"خطأ في تحميل أيقونة البرنامج: {e}")

            # إنشاء تخطيط النافذة مع مسافات مناسبة
            layout = QVBoxLayout(choice_dialog)
            layout.setContentsMargins(30, 25, 30, 25)  # حشو مناسب
            layout.setSpacing(20)  # مسافات جيدة بين العناصر

            # إضافة عنوان رئيسي منسق وملون
            title_label = QLabel("اختر طريقة طباعة الاستدعاءات")
            title_label.setFont(QFont("Calibri", 14, QFont.Bold))  # خط العناوين
            title_label.setAlignment(Qt.AlignCenter)
            title_label.setStyleSheet("color: #000080; padding: 10px;")  # أزرق غامق مع حشو
            layout.addWidget(title_label)
            
            # إضافة مسافة تزيينية
            layout.addSpacing(15)

            # إضافة مربع الاختيار للمؤسسات مع تسمية منسقة وملونة
            institution_label = QLabel("اختر المؤسسة:")
            institution_label.setFont(QFont("Calibri", 14, QFont.Bold))  # خط العناوين
            institution_label.setIndent(10)  # مسافة بادئة للتنسيق
            institution_label.setStyleSheet("color: #000080; padding: 5px;")  # أزرق غامق
            layout.addWidget(institution_label)

            # إضافة مربع الاختيار مع تحسينات وتنسيق أنيق
            combo_box = QComboBox()
            combo_box.setFont(QFont("Calibri", 13, QFont.Bold))  # الخط الافتراضي
            combo_box.setMinimumHeight(40)  # ارتفاع مناسب
            combo_box.setStyleSheet("""
                QComboBox {
                    color: #000000;
                    background-color: white;
                    border: 2px solid #000080;
                    border-radius: 8px;
                    padding: 8px;
                }
                QComboBox:focus {
                    border: 2px solid #0066CC;
                }
                QComboBox::drop-down {
                    border: none;
                    width: 25px;
                }
            """)
            combo_box.addItem("طباعة جميع الاستدعاءات")

            # الحصول على قائمة المؤسسات الأصلية من قاعدة البيانات
            institutions = []
            try:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()

                # محاولة الحصول على المؤسسات الأصلية (مع مراعاة الاسمين المحتملين للعمود)
                try:
                    cursor.execute("SELECT DISTINCT \"المؤسسة_الأصلية\" FROM امتحانات WHERE \"المؤسسة_الأصلية\" IS NOT NULL AND \"المؤسسة_الأصلية\" != ''")
                    institutions = [row[0] for row in cursor.fetchall()]
                except:
                    try:
                        cursor.execute("SELECT DISTINCT \"المؤسسة-الأصلية\" FROM امتحانات WHERE \"المؤسسة-الأصلية\" IS NOT NULL AND \"المؤسسة-الأصلية\" != ''")
                        institutions = [row[0] for row in cursor.fetchall()]
                    except:
                        pass

                conn.close()
            except Exception as e:
                print(f"خطأ في الحصول على قائمة المؤسسات: {e}")

            # إضافة المؤسسات إلى مربع الاختيار مع عدد الاستدعاءات لكل مؤسسة
            if institutions:
                # الحصول على عدد المترشحين لكل مؤسسة
                institution_counts = {}
                try:
                    conn = sqlite3.connect(self.db_path)
                    cursor = conn.cursor()

                    # محاولة الحصول على عدد المترشحين لكل مؤسسة (مع مراعاة الاسمين المحتملين للعمود)
                    try:
                        cursor.execute("SELECT \"المؤسسة_الأصلية\", COUNT(*) FROM امتحانات WHERE \"المؤسسة_الأصلية\" IS NOT NULL AND \"المؤسسة_الأصلية\" != '' GROUP BY \"المؤسسة_الأصلية\"")
                        institution_counts = {row[0]: row[1] for row in cursor.fetchall()}
                    except:
                        try:
                            cursor.execute("SELECT \"المؤسسة-الأصلية\", COUNT(*) FROM امتحانات WHERE \"المؤسسة-الأصلية\" IS NOT NULL AND \"المؤسسة-الأصلية\" != '' GROUP BY \"المؤسسة-الأصلية\"")
                            institution_counts = {row[0]: row[1] for row in cursor.fetchall()}
                        except:
                            pass

                    conn.close()
                except Exception as e:
                    print(f"خطأ في الحصول على عدد المترشحين لكل مؤسسة: {e}")

                # إضافة المؤسسات مع عدد المترشحين
                for institution in institutions:
                    count = institution_counts.get(institution, 0)
                    combo_box.addItem(f"المؤسسة: {institution} ({count} استدعاء)")

            layout.addWidget(combo_box)

            # إضافة مسافة تزيينية قبل الأزرار
            layout.addSpacing(25)

            # إضافة أزرار موافق/إلغاء ملونة وأنيقة
            button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
            
            # تحسين زر الموافقة - أزرق أنيق
            ok_button = button_box.button(QDialogButtonBox.Ok)
            ok_button.setText("موافق")
            ok_button.setFont(QFont("Calibri", 13, QFont.Bold))
            ok_button.setMinimumSize(120, 40)
            ok_button.setStyleSheet("""
                QPushButton {
                    background-color: #000080;
                    color: white;
                    border: none;
                    border-radius: 10px;
                    padding: 10px;
                }
                QPushButton:hover {
                    background-color: #0066CC;
                    transform: scale(1.02);
                }
                QPushButton:pressed {
                    background-color: #000066;
                }
            """)
            
            # تحسين زر الإلغاء - رمادي أنيق
            cancel_button = button_box.button(QDialogButtonBox.Cancel)
            cancel_button.setText("إلغاء")
            cancel_button.setFont(QFont("Calibri", 13, QFont.Bold))
            cancel_button.setMinimumSize(120, 40)
            cancel_button.setStyleSheet("""
                QPushButton {
                    background-color: #666666;
                    color: white;
                    border: none;
                    border-radius: 10px;
                    padding: 10px;
                }
                QPushButton:hover {
                    background-color: #777777;
                    transform: scale(1.02);
                }
                QPushButton:pressed {
                    background-color: #555555;
                }
            """)
            
            button_box.setCenterButtons(True)

            # ربط الأزرار بالإجراءات
            button_box.accepted.connect(choice_dialog.accept)
            button_box.rejected.connect(choice_dialog.reject)

            layout.addWidget(button_box)

            # عرض النافذة وانتظار الرد
            if choice_dialog.exec_() != QDialog.Accepted:
                return

            # الحصول على الاختيار
            selected_option = combo_box.currentText()

            # تحديد معايير التصفية
            filter_criteria = None
            if selected_option.startswith("المؤسسة:"):
                # استخراج اسم المؤسسة من النص (إزالة عدد الاستدعاءات)
                institution_text = selected_option.replace("المؤسسة: ", "")
                institution_name = institution_text.split(" (")[0]
                filter_criteria = {"المؤسسة_الأصلية": institution_name}

            # إنشاء شريط تحميل منسق وجميل
            progress = QProgressDialog("جاري التحضير لإنشاء استدعاءات المترشحين...", "إلغاء", 0, 100, self)
            progress.setWindowTitle("إنشاء استدعاءات المترشحين")
            progress.setWindowModality(Qt.WindowModal)
            progress.setMinimumDuration(0)
            progress.setAutoClose(False)
            progress.setAutoReset(False)
            progress.setMinimumWidth(650)
            progress.setMinimumHeight(200)

            # إضافة أيقونة البرنامج لشريط التحميل
            try:
                app_icon = QIcon("01.ico")
                progress.setWindowIcon(app_icon)
            except:
                pass

            # تحسين مظهر شريط التحميل مع تصميم عصري وجذاب
            progress.setStyleSheet("""
                QProgressDialog {
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                               stop: 0 #f8f9fa, stop: 0.5 #e9ecef, stop: 1 #dee2e6);
                    border: 3px solid #007bff;
                    border-radius: 20px;
                    padding: 25px;
                    font-family: 'Segoe UI', 'Calibri';
                }
                QLabel {
                    color: #495057;
                    font-family: 'Segoe UI', 'Calibri';
                    font-size: 14pt;
                    font-weight: bold;
                    background: transparent;
                    padding: 10px;
                    border: none;
                    text-align: center;
                }
                QProgressBar {
                    border: 3px solid #007bff;
                    border-radius: 15px;
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                               stop: 0 #ffffff, stop: 1 #f8f9fa);
                    text-align: center;
                    color: #212529;
                    font-family: 'Segoe UI', 'Calibri';
                    font-weight: bold;
                    font-size: 13pt;
                    min-height: 35px;
                    max-height: 35px;
                }
                QProgressBar::chunk {
                    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                               stop: 0 #28a745, stop: 0.3 #20c997,
                                               stop: 0.6 #17a2b8, stop: 1 #007bff);
                    border-radius: 12px;
                    margin: 2px;
                    animation: progress-animation 2s infinite;
                }
                QPushButton {
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                               stop: 0 #dc3545, stop: 1 #c82333);
                    color: white;
                    border-radius: 12px;
                    padding: 12px 20px;
                    font-family: 'Segoe UI', 'Calibri';
                    font-weight: bold;
                    font-size: 12pt;
                    min-width: 140px;
                    border: 3px solid #c82333;
                }
                QPushButton:hover {
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                               stop: 0 #c82333, stop: 1 #bd2130);
                    border: 3px solid #dc3545;
                    transform: scale(1.05);
                }
                QPushButton:pressed {
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                               stop: 0 #bd2130, stop: 1 #a71e2a);
                    transform: scale(0.98);
                }
            """)

            # إظهار شريط التحميل
            progress.setValue(0)
            progress.show()
            QApplication.processEvents()
            time.sleep(0.2)

            # محاولة استدعاء دالة إنشاء استدعاءات المترشحين
            try:
                # المرحلة 1: التحضير الأولي
                progress.setLabelText("🔧 جاري تحضير البيانات والمعايير...")
                progress.setValue(5)
                QApplication.processEvents()
                time.sleep(0.3)

                print("🚀 بدء عملية إنشاء استدعاءات المترشحين...")

                # المرحلة 2: إعداد المجلدات
                progress.setLabelText("📁 جاري إعداد مجلدات الحفظ...")
                progress.setValue(10)
                QApplication.processEvents()
                time.sleep(0.2)

                # الحصول على مجلد التنزيلات
                downloads_folder = get_downloads_folder()

                # إنشاء مجلد للتقارير داخل مجلد التنزيلات
                reports_folder = os.path.join(downloads_folder, "تقارير برنامج المعين في الحراسة العامة")
                os.makedirs(reports_folder, exist_ok=True)

                # المرحلة 3: جلب بيانات المترشحين
                progress.setLabelText("📊 جاري جلب بيانات المترشحين من قاعدة البيانات...")
                progress.setValue(20)
                QApplication.processEvents()
                time.sleep(0.3)

                # حساب عدد المترشحين المتوقع
                total_candidates = 0
                try:
                    conn = sqlite3.connect(self.db_path)
                    cursor = conn.cursor()

                    if filter_criteria:
                        if "المؤسسة_الأصلية" in filter_criteria:
                            cursor.execute("SELECT COUNT(*) FROM امتحانات WHERE \"المؤسسة_الأصلية\" = ?",
                                         (filter_criteria["المؤسسة_الأصلية"],))
                    else:
                        cursor.execute("SELECT COUNT(*) FROM امتحانات")

                    total_candidates = cursor.fetchone()[0]
                    conn.close()
                except Exception as e:
                    print(f"خطأ في حساب عدد المترشحين: {e}")

                # المرحلة 4: إعداد نوع التقرير
                progress.setLabelText(f"🎯 جاري إعداد التقرير ({total_candidates} مترشح)...")
                progress.setValue(30)
                QApplication.processEvents()
                time.sleep(0.2)

                # استخدام print12.py للطباعة العادية (استدعاء واحد في كل صفحة)
                try:
                    progress.setLabelText("📄 جاري تحميل وحدة الطباعة...")
                    progress.setValue(40)
                    QApplication.processEvents()
                    time.sleep(0.2)

                    import print12

                    progress.setLabelText("🖨️ جاري إنشاء ملف PDF...")
                    progress.setValue(50)
                    QApplication.processEvents()
                    time.sleep(0.3)

                    # استدعاء دالة إنشاء التقرير من ملف print12.py
                    success, output_path, message = print12.print_exams_report(
                        parent=self,
                        report_title=title1,
                        sub_title=title1,
                        filter_criteria=filter_criteria,
                        output_dir=reports_folder
                    )

                    # التأكد من إنشاء التقرير بنجاح قبل تطبيق الخلفية
                    if success and output_path:
                        print("✅ تم إنشاء استدعاءات المترشحين الأساسية بنجاح")
                        print(f"📁 مسار التقرير: {output_path}")
                        
                        # تأخير قصير للتأكد من حفظ الملف بالكامل
                        progress.setLabelText("⏳ جاري الانتظار لضمان حفظ التقرير...")
                        progress.setValue(60)
                        QApplication.processEvents()
                        time.sleep(1)  # تأخير ثانية واحدة
                        
                        # التحقق من وجود الملف فعلياً
                        if os.path.exists(output_path):
                            print(f"✅ تم التأكد من وجود الملف: {output_path}")
                            
                            # التحقق من صحة ملف التقرير
                            if self.verify_report_file(output_path):
                                print("✅ ملف التقرير صالح ومقروء")
                                
                                # محاولة تطبيق الخلفية المحفوظة من جدول بيانات_المؤسسة
                                print("🎨 بدء البحث عن خلفية محفوظة لتطبيقها على استدعاءات المترشحين...")
                                
                                # البحث عن الخلفية المحفوظة
                                background_path = self.get_background_path()
                                background_applied = False
                                
                                if background_path:
                                    print(f"🔍 تم العثور على مسار خلفية: {background_path}")
                                    
                                    # اختبار الخلفية أولاً
                                    if self.test_background_before_applying(background_path):
                                        # تحديث شريط التقدم - تطبيق الخلفية
                                        progress.setLabelText("🎨 جاري تطبيق الخلفية المحسنة على استدعاءات المترشحين...")
                                        progress.setValue(75)
                                        QApplication.processEvents()
                                        time.sleep(0.3)
                                        
                                        print(f"📁 بدء تطبيق الخلفية من: {background_path}")
                                        
                                        try:
                                            # تأكد من أن مسار الخلفية محفوظ في الذاكرة
                                            self.background_pdf_path = background_path
                                            
                                            # تطبيق الخلفية باستخدام الطريقة المحسنة المُحدثة
                                            background_applied = self.apply_background_to_existing_pdf(output_path, background_path)
                                            
                                            if background_applied:
                                                print("🎉 تم تطبيق الخلفية على استدعاءات المترشحين بنجاح!")
                                                
                                                # التحقق من النتيجة النهائية
                                                if os.path.exists(output_path):
                                                    file_size = os.path.getsize(output_path)
                                                    print(f"📊 حجم الملف النهائي: {file_size} بايت")
                                                else:
                                                    print("⚠️ الملف النهائي غير موجود!")
                                            else:
                                                print("⚠️ فشل في تطبيق الخلفية على استدعاءات المترشحين")
                                                
                                        except Exception as bg_error:
                                            print(f"❌ خطأ في تطبيق الخلفية على استدعاءات المترشحين: {bg_error}")
                                            import traceback
                                            traceback.print_exc()
                                            background_applied = False
                                    else:
                                        print("❌ اختبار الخلفية فشل - لن يتم تطبيقها")
                                else:
                                    print("ℹ️ لا توجد خلفية محفوظة - سيتم إنشاء استدعاءات المترشحين بدون خلفية")
                                
                                # تحديث شريط التقدم - التحقق من سلامة الملف النهائي
                                progress.setLabelText("🔍 جاري التحقق من سلامة الملف النهائي...")
                                progress.setValue(85)
                                QApplication.processEvents()
                                time.sleep(0.3)
                                
                                # التحقق من سلامة الملف النهائي قبل فتحه
                                final_file_valid = False
                                try:
                                    if self.verify_report_file(output_path):
                                        print("✅ الملف النهائي صالح وجاهز للفتح")
                                        final_file_valid = True
                                    else:
                                        print("❌ الملف النهائي تالف أو غير قابل للقراءة")
                                except Exception as verify_error:
                                    print(f"⚠️ خطأ في التحقق من الملف النهائي: {verify_error}")
                                
                                # فتح الملف النهائي فقط إذا كان صالحاً
                                if final_file_valid:
                                    progress.setLabelText("📂 جاري فتح استدعاءات المترشحين...")
                                    progress.setValue(90)
                                    QApplication.processEvents()
                                    
                                    try:
                                        if sys.platform == 'win32':
                                            os.startfile(output_path)
                                        elif sys.platform == 'darwin':  # macOS
                                            subprocess.call(['open', output_path])
                                        else:  # Linux
                                            subprocess.call(['xdg-open', output_path])
                                        
                                        if background_applied:
                                            print("🎉 تم فتح استدعاءات المترشحين مع الخلفية بنجاح!")
                                        else:
                                            print("📄 تم فتح استدعاءات المترشحين بدون خلفية")
                                            
                                    except Exception as open_error:
                                        print(f"⚠️ تعذر فتح الملف: {open_error}")
                                        self.show_message("تنبيه", f"تم إنشاء التقرير ولكن تعذر فتح الملف: {str(open_error)}", "warning")
                                else:
                                    print("❌ لن يتم فتح الملف لأنه تالف")
                                    self.show_message("خطأ", "تم إنشاء التقرير ولكنه تالف. يرجى المحاولة مرة أخرى بدون خلفية.", "error")
                                    
                            else:
                                print("❌ ملف التقرير تالف أو غير مقروء - لن يتم تطبيق الخلفية")
                        else:
                            print(f"❌ لم يتم العثور على الملف المُنشأ: {output_path}")
                    else:
                        print(f"❌ فشل في إنشاء التقرير: {message}")

                    # تحديث التقدم بعد اكتمال العمليات
                    progress.setLabelText("🎉 اكتملت العملية بنجاح!")
                    progress.setValue(100)
                    QApplication.processEvents()
                    time.sleep(0.5)

                except ImportError:
                    progress.close()
                    self.show_message("خطأ", "لم يتم العثور على ملف print12.py اللازم لإنشاء استدعاءات المترشحين.", "error")
                    return
                except Exception as e:
                    progress.close()
                    self.show_message("خطأ", f"حدث خطأ أثناء إنشاء استدعاءات المترشحين: {str(e)}", "error")
                    return

                # المرحلة الأخيرة: إنهاء العملية
                print("📂 تم إنهاء جميع العمليات...")

                # إغلاق شريط التحميل
                progress.close()

                if success:
                    # تحديد عدد الاستدعاءات التي تم إنشاؤها
                    num_invitations = 0
                    if selected_option.startswith("المؤسسة:"):
                        # استخراج عدد الاستدعاءات من النص
                        try:
                            count_text = selected_option.split("(")[1].split(" استدعاء")[0]
                            num_invitations = int(count_text)
                        except:
                            pass
                    else:
                        # الحصول على إجمالي عدد المترشحين
                        try:
                            conn = sqlite3.connect(self.db_path)
                            cursor = conn.cursor()
                            cursor.execute("SELECT COUNT(*) FROM امتحانات")
                            num_invitations = cursor.fetchone()[0]
                            conn.close()
                        except:
                            pass

                    # إنشاء رسالة النجاح المحسنة مع معلومات الخلفية
                    success_message = f"✅ تم إنشاء استدعاءات المترشحين بنجاح"
                    
                    # إضافة معلومة عن الخلفية إذا كانت متاحة
                    background_path = self.get_background_path()
                    if background_path:
                        success_message += " مع تطبيق الخلفية المحفوظة"
                    else:
                        success_message += " (بدون خلفية - لم يتم تحديد خلفية مسبقاً)"
                    
                    success_message += "!\n\n📊 تفاصيل العملية:\n"
                    
                    if num_invitations > 0:
                        success_message += f"• عدد الاستدعاءات المُنشأة: {num_invitations}\n"
                        success_message += f"• نوع الطباعة: استدعاء واحد في كل صفحة\n"
                        success_message += f"• عدد الصفحات المتوقع: {num_invitations}\n"

                    if filter_criteria and "المؤسسة_الأصلية" in filter_criteria:
                        success_message += f"• المؤسسة المحددة: {filter_criteria['المؤسسة_الأصلية']}\n"
                    else:
                        success_message += f"• النطاق: جميع المترشحين\n"

                    success_message += f"\n📁 مسار الملف:\n{output_path}"

                    self.show_message(
                        "نجح إنشاء الاستدعاءات!",
                        success_message,
                        "success"
                    )
                else:
                    self.show_message("تنبيه", f"لم يتم إنشاء استدعاءات المترشحين: {message}", "warning")

            except Exception as e:
                # إغلاق شريط التحميل في حالة الخطأ
                if 'progress' in locals():
                    progress.close()
                self.show_message("خطأ", f"حدث خطأ أثناء إنشاء استدعاءات المترشحين: {str(e)}", "error")
        except Exception as e:
            self.show_message("خطأ", f"حدث خطأ أثناء إنشاء استدعاءات المترشحين: {str(e)}", "error")
        finally:
            # إعادة عنوان النافذة الأصلي
            self.setWindowTitle("إدارة التقارير واللوائح والاستدعاءات")

    def statistics(self):
        """إنشاء تقرير الإحصاءات"""
        try:
            # تعيين عنوان النافذة
            self.setWindowTitle("إحصاءات الامتحانات - إدارة التقارير واللوائح والاستدعاءات")

            # التحقق من إدخال البيانات المطلوبة
            title3 = self.text_fields["العنوان3"].text().strip()

            if not title3:
                self.show_message("تنبيه", "الرجاء إدخال عنوان الإحصائيات على الأقل.", "warning")
                return

            # إنشاء شريط تقدم العملية محسن
            progress = QProgressDialog("جاري إنشاء تقرير الإحصائيات...", "إلغاء", 0, 100, self)
            progress.setWindowTitle("إنشاء تقرير الإحصائيات")
            progress.setWindowModality(Qt.WindowModal)
            progress.setMinimumDuration(0)
            progress.setAutoClose(True)
            progress.setAutoReset(True)
            progress.setMinimumWidth(400)

            # تحسين مظهر شريط التقدم
            progress.setStyleSheet("""
                QProgressDialog {
                    background-color: #f0f8ff;
                    border: 2px solid #3498db;
                    border-radius: 10px;
                    padding: 10px;
                }
                QLabel {
                    color: #2980b9;
                    font-family: Calibri;
                    font-size: 12pt;
                    font-weight: bold;
                }
                QProgressBar {
                    border: 1px solid #3498db;
                    border-radius: 5px;
                    background-color: #ecf0f1;
                    text-align: center;
                    color: black;
                    font-family: Calibri;
                    font-weight: bold;
                    min-height: 20px;
                }
                QProgressBar::chunk {
                    background-color: #3498db;
                    width: 10px;
                    margin: 0.5px;
                }
                QPushButton {
                    background-color: #e74c3c;
                    color: white;
                    border-radius: 5px;
                    padding: 5px 10px;
                    font-family: Calibri;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #c0392b;
                }
            """)

            progress.setValue(0)
            progress.show()

            # استيراد وحدة time للتأخير
            import time

            # تحديث شريط التقدم - بدء العملية
            progress.setLabelText("جاري تهيئة العملية...")
            progress.setValue(10)
            time.sleep(0.3)

            # محاولة استدعاء دالة إنشاء تقرير الإحصاءات من ملف print15.py
            try:
                # تحديث شريط التقدم - جلب البيانات
                progress.setLabelText("جاري جلب بيانات المترشحين...")
                progress.setValue(30)
                time.sleep(0.3)

                # استيراد ملف print15
                import print15

                # تحديث شريط التقدم - إنشاء التقرير
                progress.setLabelText("جاري إنشاء ملف PDF...")
                progress.setValue(50)

                # الحصول على مجلد التنزيلات
                downloads_folder = get_downloads_folder()

                # إنشاء مجلد للتقارير داخل مجلد التنزيلات
                reports_folder = os.path.join(downloads_folder, "تقارير برنامج المعين في الحراسة العامة")
                os.makedirs(reports_folder, exist_ok=True)

                # تحديد اسم الملف مع طابع زمني
                from datetime import datetime
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                output_filename = f"تقرير_الاحصاءات_{timestamp}.pdf"
                output_path = os.path.join(reports_folder, output_filename)

                # استدعاء دالة إنشاء تقرير الإحصاءات
                success, final_output_path, message = print15.print_exams_report(
                    parent=self,
                    level=None,  # جميع المستويات
                    report_title=title3,
                    subject_data=None
                )

                # تطبيق الخلفية إذا كانت متاحة
                if success and final_output_path and hasattr(self, 'background_pdf_path') and self.background_pdf_path:
                    # تحديث شريط التقدم - تطبيق الخلفية
                    progress.setLabelText("جاري تطبيق الخلفية...")
                    progress.setValue(70)
                    QApplication.processEvents()
                    time.sleep(0.3)
                    
                    try:
                        # تطبيق الخلفية على التقرير
                        background_applied = self.apply_background_to_pdf(final_output_path)
                        if background_applied:
                            print("✅ تم تطبيق الخلفية على تقرير الإحصاءات بنجاح")
                        else:
                            print("⚠️ لم يتم تطبيق الخلفية على تقرير الإحصاءات")
                    except Exception as bg_error:
                        print(f"⚠️ خطأ في تطبيق الخلفية على تقرير الإحصاءات: {bg_error}")
                else:
                    # التحقق من وجود خلفية محفوظة في قاعدة البيانات
                    background_path = self.get_background_path()
                    if background_path and success and final_output_path:
                        # تحديث شريط التقدم - تطبيق الخلفية
                        progress.setLabelText("جاري تطبيق الخلفية...")
                        progress.setValue(70)
                        QApplication.processEvents()
                        time.sleep(0.3)
                        
                        try:
                            self.background_pdf_path = background_path
                            background_applied = self.apply_background_to_pdf(final_output_path)
                            if background_applied:
                                print("✅ تم تطبيق الخلفية على تقرير الإحصاءات بنجاح")
                            else:
                                print("⚠️ لم يتم تطبيق الخلفية على تقرير الإحصاءات")
                        except Exception as bg_error:
                            print(f"⚠️ خطأ في تطبيق الخلفية على تقرير الإحصاءات: {bg_error}")

                # تحديث شريط التقدم - اكتمال العملية
                progress.setLabelText("جاري فتح الملف...")
                progress.setValue(80)
                QApplication.processEvents()  # معالجة الأحداث المعلقة
                time.sleep(0.3)

                # تحديث شريط التقدم - اكتمال العملية
                progress.setLabelText("اكتملت العملية بنجاح!")
                progress.setValue(100)
                QApplication.processEvents()  # معالجة الأحداث المعلقة
                time.sleep(0.5)

                if success:
                    # الحصول على عدد المترشحين الإجمالي
                    total_candidates = 0
                    total_rooms = 0
                    total_levels = 0

                    try:
                        conn = sqlite3.connect(self.db_path)
                        cursor = conn.cursor()

                        # عدد المترشحين الإجمالي
                        cursor.execute("SELECT COUNT(*) FROM امتحانات")
                        total_candidates = cursor.fetchone()[0]

                        # عدد القاعات
                        cursor.execute("SELECT COUNT(DISTINCT القاعة) FROM امتحانات WHERE القاعة IS NOT NULL AND القاعة != ''")
                        total_rooms = cursor.fetchone()[0]

                        # عدد المستويات
                        cursor.execute("SELECT COUNT(DISTINCT المستوى) FROM امتحانات WHERE المستوى IS NOT NULL AND المستوى != ''")
                        total_levels = cursor.fetchone()[0]

                        conn.close()
                    except Exception as e:
                        print(f"خطأ في الحصول على الإحصاءات: {e}")

                    # إنشاء رسالة النجاح التفصيلية
                    success_message = f"تم إنشاء تقرير الإحصاءات بنجاح وفتحه.\n\n"
                    success_message += f"ملخص الإحصاءات:\n"
                    success_message += f"• إجمالي المترشحين: {total_candidates}\n"
                    success_message += f"• عدد القاعات: {total_rooms}\n"
                    success_message += f"• عدد المستويات: {total_levels}\n\n"
                    success_message += f"تم حفظ الملف في مجلد التنزيلات:\n{final_output_path or output_path}"

                    self.show_message("نجاح", success_message, "success")
                else:
                    self.show_message("تنبيه", f"لم يتم إنشاء تقرير الإحصاءات: {message}", "warning")

            except ImportError:
                self.show_message("خطأ", "لم يتم العثور على ملف print15.py اللازم لإنشاء تقرير الإحصاءات.", "error")
            except Exception as e:
                self.show_message("خطأ", f"حدث خطأ أثناء إنشاء تقرير الإحصاءات: {str(e)}", "error")

        except Exception as e:
            self.show_message("خطأ", f"حدث خطأ أثناء إنشاء تقرير الإحصاءات: {str(e)}", "error")
        finally:
            # إعادة عنوان النافذة الأصلي
            self.setWindowTitle("إدارة التقارير واللوائح والاستدعاءات")

    def attendance_all_subjects(self):
        """إنشاء لوائح المترشحين مجمعة حسب القاعات فقط"""
        try:
            # تعيين عنوان النافذة
            self.setWindowTitle("لوائح المترشحين - إدارة التقارير واللوائح والاستدعاءات")

            # التحقق من إدخال البيانات المطلوبة
            title2 = self.text_fields["العنوان2"].text().strip()

            if not title2:
                self.show_message("تنبيه", "الرجاء إدخال عنوان لائحة الحضور على الأقل.", "warning")
                return

            # إنشاء شريط تقدم العملية محسن
            progress = QProgressDialog("جاري إنشاء لائحة المترشحين...", "إلغاء", 0, 100, self)
            progress.setWindowTitle("إنشاء لائحة المترشحين")
            progress.setWindowModality(Qt.WindowModal)
            progress.setMinimumDuration(0)
            progress.setAutoClose(True)
            progress.setAutoReset(True)
            progress.setMinimumWidth(400)

            # تحسين مظهر شريط التقدم
            progress.setStyleSheet("""
                QProgressDialog {
                    background-color: #f0f8ff;
                    border: 2px solid #3498db;
                    border-radius: 10px;
                    padding: 10px;
                }
                QLabel {
                    color: #2980b9;
                    font-family: Calibri;
                    font-size: 12pt;
                    font-weight: bold;
                }
                QProgressBar {
                    border: 1px solid #3498db;
                    border-radius: 5px;
                    background-color: #ecf0f1;
                    text-align: center;
                    color: black;
                    font-family: Calibri;
                    font-weight: bold;
                    min-height: 20px;
                }
                QProgressBar::chunk {
                    background-color: #3498db;
                    width: 10px;
                    margin: 0.5px;
                }
                QPushButton {
                    background-color: #e74c3c;
                    color: white;
                    border-radius: 5px;
                    padding: 5px 10px;
                    font-family: Calibri;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #c0392b;
                }
            """)

            progress.setValue(0)
            progress.show()

            # استيراد وحدة time للتأخير
            import time

            # تحديث شريط التقدم - بدء العملية
            progress.setLabelText("جاري تهيئة العملية...")
            progress.setValue(10)
            time.sleep(0.3)  # إضافة تأخير قصير لعرض التقدم

            # التحقق من وجود بيانات في قاعدة البيانات
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("SELECT COUNT(*) FROM امتحانات")
            count = cursor.fetchone()[0]
            conn.close()

            if count == 0:
                self.show_message("تنبيه", "لم يتم العثور على أي بيانات في قاعدة البيانات.", "warning")
                return

            # تحديث شريط التقدم - جلب البيانات
            progress.setLabelText("جاري جلب بيانات المترشحين...")
            progress.setValue(30)
            QApplication.processEvents()
            time.sleep(0.3)

            # إنشاء لائحة المترشحين مجمعة حسب القاعات فقط (بدون تحديد مستوى)
            try:
                # تحديث شريط التقدم - إنشاء التقرير
                progress.setLabelText("جاري إنشاء ملف PDF...")
                progress.setValue(50)
                QApplication.processEvents()
                time.sleep(0.3)

                # استيراد ملف print13.py
                import print13

                # إنشاء تقرير واحد مجمع لجميع المترشحين حسب القاعات
                progress.setLabelText("جاري إنشاء لائحة المترشحين مجمعة حسب القاعات...")
                progress.setValue(70)
                QApplication.processEvents()

                # استدعاء الدالة بدون تحديد مستوى لإنشاء تقرير مجمع
                success, output_path, message = print13.print_exams_report(self, level=None, report_title=title2)

                # التأكد من إنشاء التقرير بنجاح قبل تطبيق الخلفية
                if success and output_path:
                    print("✅ تم إنشاء التقرير الأساسي بنجاح")
                    print(f"📁 مسار التقرير: {output_path}")
                    
                    # تأخير قصير للتأكد من حفظ الملف بالكامل
                    progress.setLabelText("⏳ جاري الانتظار لضمان حفظ التقرير...")
                    progress.setValue(75)
                    QApplication.processEvents()
                    time.sleep(1)  # تأخير ثانية واحدة
                    
                    # التحقق من وجود الملف فعلياً
                    if os.path.exists(output_path):
                        print(f"✅ تم التأكد من وجود الملف: {output_path}")
                        
                        # التحقق من صحة ملف التقرير
                        if self.verify_report_file(output_path):
                            print("✅ ملف التقرير صالح ومقروء")
                            
                            # محاولة تطبيق الخلفية المحفوظة من جدول بيانات_المؤسسة
                            print("🎨 بدء البحث عن خلفية محفوظة لتطبيقها على لائحة المترشحين...")
                            
                            # البحث عن الخلفية المحفوظة
                            background_path = self.get_background_path()
                            
                            if background_path:
                                print(f"🔍 تم العثور على مسار خلفية: {background_path}")
                                
                                # اختبار الخلفية أولاً
                                if self.test_background_before_applying(background_path):
                                    # تحديث شريط التقدم - تطبيق الخلفية
                                    progress.setLabelText("🎨 جاري تطبيق الخلفية المحسنة...")
                                    progress.setValue(85)
                                    QApplication.processEvents()
                                    time.sleep(0.3)
                                    
                                    print(f"📁 بدء تطبيق الخلفية من: {background_path}")
                                    
                                    try:
                                        # تأكد من أن مسار الخلفية محفوظ في الذاكرة
                                        self.background_pdf_path = background_path
                                        
                                        # تطبيق الخلفية باستخدام الطريقة المحسنة
                                        background_applied = self.apply_background_to_existing_pdf(output_path, background_path)
                                        
                                        if background_applied:
                                            print("🎉 تم تطبيق الخلفية على لائحة المترشحين بنجاح!")
                                            
                                            # التحقق من النتيجة النهائية
                                            if os.path.exists(output_path):
                                                file_size = os.path.getsize(output_path)
                                                print(f"📊 حجم الملف النهائي: {file_size} بايت")
                                            else:
                                                print("⚠️ الملف النهائي غير موجود!")
                                        else:
                                            print("⚠️ فشل في تطبيق الخلفية على لائحة المترشحين")
                                            
                                    except Exception as bg_error:
                                        print(f"❌ خطأ في تطبيق الخلفية على لائحة المترشحين: {bg_error}")
                                        import traceback
                                        traceback.print_exc()
                                else:
                                    print("❌ اختبار الخلفية فشل - لن يتم تطبيقها")
                            else:
                                print("ℹ️ لا توجد خلفية محفوظة - سيتم إنشاء لائحة المترشحين بدون خلفية")
                        else:
                            print("❌ ملف التقرير تالف أو غير مقروء - لن يتم تطبيق الخلفية")
                    else:
                        print(f"❌ لم يتم العثور على الملف المُنشأ: {output_path}")
                else:
                    print(f"❌ فشل في إنشاء التقرير: {message}")

                # تحديث شريط التقدم - اكتمال العملية
                progress.setLabelText("اكتملت العملية بنجاح!")
                progress.setValue(100)
                QApplication.processEvents()  # معالجة الأحداث المعلقة
                time.sleep(0.5)

            except ImportError:
                self.show_message("خطأ", "لم يتم العثور على ملف print13.py اللازم لإنشاء لوائح المترشحين.", "error")
                return
            except Exception as e:
                self.show_message("خطأ", f"حدث خطأ أثناء إنشاء لوائح المترشحين: {str(e)}", "error")
                return

            if success:
                self.show_message("نجاح", "تم إنشاء لائحة المترشحين مجمعة حسب القاعات بنجاح.", "success")
            else:
                self.show_message("تنبيه", f"لم يتم إنشاء لائحة المترشحين: {message}", "warning")
        except Exception as e:
            self.show_message("خطأ", f"حدث خطأ أثناء إنشاء لوائح المترشحين: {str(e)}", "error")
        finally:
            # إعادة عنوان النافذة الأصلي
            self.setWindowTitle("إدارة التقارير واللوائح والاستدعاءات")

    def table_labels(self):
        """إنشاء ملصقات الطاولات"""
        try:
            # تعيين عنوان النافذة
            self.setWindowTitle("ملصقات الطاولات - إدارة التقارير واللوائح والاستدعاءات")

            # التحقق من إدخال البيانات المطلوبة
            title4 = self.text_fields["العنوان4"].text().strip()

            if not title4:
                self.show_message("تنبيه", "الرجاء إدخال عنوان جدولة الامتحان على الأقل.", "warning")
                return

            try:
                # استيراد نافذة ملصقات الطاولات من ملف sub27_window.py
                import importlib.util

                # تحديد مسار الملف
                module_path = os.path.join(os.path.dirname(__file__), 'sub27_window.py')

                # التحقق من وجود الملف
                if not os.path.exists(module_path):
                    self.show_message("خطأ", "ملف sub27_window.py غير موجود في المجلد الحالي.", "error")
                    return

                # استيراد الوحدة بشكل ديناميكي
                spec = importlib.util.spec_from_file_location("sub27_window", module_path)
                sub27_module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(sub27_module)

                # نقوم بالتحقق من اسم الكلاس المستخدم في ملف sub27_window.py
                window = None
                if hasattr(sub27_module, "Sub27Window"):
                    # استخدام الفئة Sub27Window
                    window = sub27_module.Sub27Window(parent=self, db_path=self.db_path)
                elif hasattr(sub27_module, "TableLabelsPDF"):
                    # استخدام الفئة TableLabelsPDF (كما في sub22_window.py)
                    window = sub27_module.TableLabelsPDF(parent=self, db_path=self.db_path)
                else:
                    self.show_message("خطأ", "لم يتم العثور على الفئة المطلوبة في ملف sub27_window.py.", "error")
                    return

                # تمرير العنوان إذا كانت الفئة تدعم تعيين العنوان
                if hasattr(window, "set_title"):
                    window.set_title(title4)

                # عرض النافذة
                window.exec_()

            except ImportError as e:
                self.show_message("خطأ", f"تعذر استيراد ملف sub27_window.py: {str(e)}", "error")
            except Exception as e:
                self.show_message("خطأ", f"حدث خطأ أثناء فتح نافذة ملصقات الطاولات: {str(e)}", "error")
        except Exception as e:
            self.show_message("خطأ", f"حدث خطأ أثناء إنشاء ملصقات الطاولات: {str(e)}", "error")
        finally:
            # إعادة عنوان النافذة الأصلي
            self.setWindowTitle("إدارة التقارير واللوائح والاستدعاءات")

    def general_instructions(self):
        """فتح نافذة التوجيهات العامة للمترشح"""
        try:
            # تعيين عنوان النافذة
            self.setWindowTitle("توجيهات عامة للمترشح (ة) - إدارة التقارير واللوائح والاستدعاءات")

            # إنشاء نافذة حوار جديدة
            instructions_dialog = QDialog(self)
            instructions_dialog.setWindowTitle("توجيهات عامة للمترشح (ة)")
            instructions_dialog.setFixedSize(1000, 510)
            instructions_dialog.setLayoutDirection(Qt.RightToLeft)

            # إضافة أيقونة البرنامج
            try:
                app_icon = QIcon("01.ico")
                instructions_dialog.setWindowIcon(app_icon)
            except Exception as e:
                print(f"خطأ في تحميل أيقونة البرنامج: {e}")

            # إنشاء تخطيط النافذة
            layout = QVBoxLayout(instructions_dialog)
            layout.setContentsMargins(5, 5, 5, 5)
            layout.setSpacing(5)

            # إضافة عنوان
            title_label = QLabel("توجيهات عامة للمترشح (ة)")
            title_label.setFont(QFont("Calibri", 18, QFont.Bold))
            title_label.setStyleSheet("color: #2980b9;")
            title_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(title_label)

            # إضافة مربع نص كبير مع محاذاة إلى اليمين
            instructions_text = QTextEdit()
            instructions_text.setFont(QFont("Calibri", 14, QFont.Bold))
            instructions_text.setStyleSheet("""
                QTextEdit {
                    border: 1px solid #3498db;
                    border-radius: 10px;
                    padding: 10px;
                    background-color: white;
                }
            """)
            instructions_text.setFixedSize(900, 400)

            # تعيين محاذاة النص إلى اليمين
            instructions_text.setLayoutDirection(Qt.RightToLeft)

            # تعيين محاذاة النص إلى اليمين باستخدام HTML
            instructions_text.setAlignment(Qt.AlignRight)

            # تحميل البيانات من قاعدة البيانات
            try:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()

                # التحقق من وجود جدول جدولة_الامتحان
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='جدولة_الامتحان'")
                table_exists = cursor.fetchone()

                if not table_exists:
                    # إنشاء جدول جدولة_الامتحان إذا لم يكن موجودًا
                    cursor.execute("""
                        CREATE TABLE جدولة_الامتحان (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            اليوم TEXT,
                            التاريخ TEXT,
                            الحصة1 TEXT,
                            التوقيت1 TEXT,
                            الحصة2 TEXT,
                            التوقيت2 TEXT,
                            الحصة3 TEXT,
                            التوقيت3 TEXT,
                            الحصة4 TEXT,
                            التوقيت4 TEXT,
                            السنة_الدراسية TEXT,
                            الأسدس TEXT,
                            تاريخ_التحديث TEXT,
                            ملاحظات TEXT
                        )
                    """)
                    conn.commit()
                    self.show_message("معلومات", "تم إنشاء جدول جدولة_الامتحان بنجاح.", "info")
                else:
                    # التحقق من وجود عمود ملاحظات في جدول جدولة_الامتحان
                    cursor.execute("PRAGMA table_info(جدولة_الامتحان)")
                    columns = cursor.fetchall()
                    has_notes_column = any(col[1] == "ملاحظات" for col in columns)

                    if not has_notes_column:
                        # إضافة عمود ملاحظات إذا لم يكن موجودًا
                        cursor.execute("ALTER TABLE جدولة_الامتحان ADD COLUMN ملاحظات TEXT")
                        conn.commit()

                # التحقق من وجود سجل بمعرف 1
                cursor.execute("SELECT id FROM جدولة_الامتحان WHERE id = 1")
                record = cursor.fetchone()

                if not record:
                    # إنشاء سجل جديد إذا لم يكن موجودًا
                    cursor.execute("""
                        INSERT INTO جدولة_الامتحان (id, ملاحظات)
                        VALUES (1, '')
                    """)
                    conn.commit()

                # استعلام عن البيانات
                cursor.execute("SELECT ملاحظات FROM جدولة_الامتحان WHERE id = 1")
                result = cursor.fetchone()

                if result:
                    # تعيين النص مع تطبيق محاذاة إلى اليمين
                    text_content = result[0] or ""
                    instructions_text.setText(text_content)

                    # تطبيق محاذاة إلى اليمين على النص
                    cursor = instructions_text.textCursor()
                    cursor.select(cursor.Document)
                    format = cursor.blockFormat()
                    format.setAlignment(Qt.AlignRight)
                    cursor.setBlockFormat(format)

                conn.close()
            except Exception as e:
                self.show_message("خطأ", f"حدث خطأ أثناء تحميل التوجيهات العامة: {str(e)}", "error")

            # إضافة مربع النص إلى التخطيط
            text_layout = QHBoxLayout()
            text_layout.addStretch()
            text_layout.addWidget(instructions_text)
            text_layout.addStretch()
            layout.addLayout(text_layout)

            # إضافة زر حفظ النص وإنشاء صورة
            save_button = QPushButton("حفظ النص وإنشاء صورة")
            save_button.setFont(QFont("Calibri", 12, QFont.Bold))
            save_button.setStyleSheet("""
                QPushButton {
                    background-color: #2ecc71;
                    color: white;
                    border-radius: 5px;
                    padding: 10px;
                    min-width: 150px;
                    min-height: 40px;
                }
                QPushButton:hover {
                    background-color: #27ae60;
                }
            """)
            save_button.setCursor(Qt.PointingHandCursor)

            # دالة لاختيار صورة من المتصفح
            def select_image_from_browser():
                try:
                    # فتح متصفح الملفات لاختيار صورة
                    file_dialog = QFileDialog()
                    file_dialog.setWindowTitle("اختيار صورة التوجيهات")
                    file_dialog.setFileMode(QFileDialog.ExistingFile)
                    file_dialog.setNameFilter("ملفات الصور (*.png *.jpg *.jpeg *.bmp *.gif)")
                    file_dialog.setViewMode(QFileDialog.Detail)

                    if file_dialog.exec_() == QFileDialog.Accepted:
                        selected_files = file_dialog.selectedFiles()
                        if selected_files:
                            source_image_path = selected_files[0]

                            # إنشاء مسار الوجهة
                            desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
                            app_folder = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة")
                            images_folder = os.path.join(app_folder, "صور التوجيهات")

                            # التأكد من وجود المجلد
                            os.makedirs(images_folder, exist_ok=True)

                            # تحديد مسار الصورة الجديد
                            destination_image_path = os.path.join(images_folder, "توجيهات_المترشح.png")

                            # نسخ الصورة المختارة إلى المجلد المطلوب
                            import shutil
                            shutil.copy2(source_image_path, destination_image_path)

                            print(f"تم نسخ الصورة المختارة إلى: {destination_image_path}")
                            return True, destination_image_path

                    return False, None
                except Exception as e:
                    self.show_message("خطأ", f"حدث خطأ أثناء اختيار الصورة: {str(e)}", "error")
                    return False, None

            # دالة لتصوير مربع النص وحفظه كصورة مع محاذاة صحيحة
            def capture_text_as_image():
                try:
                    # إنشاء مسار للصورة
                    desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
                    app_folder = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة")
                    images_folder = os.path.join(app_folder, "صور التوجيهات")

                    # التأكد من وجود المجلد
                    os.makedirs(images_folder, exist_ok=True)

                    # تحديد مسار الصورة
                    image_path = os.path.join(images_folder, "توجيهات_المترشح.png")

                    # تطبيق محاذاة إلى اليمين قبل التصوير
                    cursor = instructions_text.textCursor()
                    cursor.select(cursor.Document)
                    format = cursor.blockFormat()
                    format.setAlignment(Qt.AlignRight)
                    cursor.setBlockFormat(format)

                    # التأكد من تحديث العرض
                    instructions_text.update()
                    QApplication.processEvents()

                    # تصوير مربع النص
                    pixmap = instructions_text.grab()
                    pixmap.save(image_path, "PNG")

                    # عرض رسالة نجاح
                    print(f"تم حفظ صورة التوجيهات محاذية إلى اليمين في: {image_path}")

                    return True, image_path
                except Exception as e:
                    self.show_message("خطأ", f"حدث خطأ أثناء تصوير التوجيهات: {str(e)}", "error")
                    return False, None

            # دالة حفظ التوجيهات
            def save_instructions():
                try:
                    conn = sqlite3.connect(self.db_path)
                    cursor = conn.cursor()

                    # التحقق من وجود سجل بمعرف 1
                    cursor.execute("SELECT id FROM جدولة_الامتحان WHERE id = 1")
                    record = cursor.fetchone()

                    if record:
                        # تحديث السجل الموجود
                        cursor.execute("UPDATE جدولة_الامتحان SET ملاحظات = ? WHERE id = 1", (instructions_text.toPlainText(),))
                    else:
                        # إنشاء سجل جديد
                        cursor.execute("INSERT INTO جدولة_الامتحان (id, ملاحظات) VALUES (1, ?)", (instructions_text.toPlainText(),))

                    conn.commit()
                    conn.close()

                    # تطبيق محاذاة إلى اليمين قبل الحفظ والتصوير
                    cursor = instructions_text.textCursor()
                    cursor.select(cursor.Document)
                    format = cursor.blockFormat()
                    format.setAlignment(Qt.AlignRight)
                    cursor.setBlockFormat(format)

                    # تصوير مربع النص وحفظه كصورة
                    capture_success = capture_text_as_image()

                    if capture_success:
                        self.show_message("نجاح", "تم حفظ التوجيهات العامة وتصويرها بمحاذاة صحيحة إلى اليمين بنجاح.", "success")
                    else:
                        self.show_message("نجاح", "تم حفظ التوجيهات العامة بنجاح، ولكن تعذر تصويرها.", "success")

                    instructions_dialog.accept()
                except Exception as e:
                    self.show_message("خطأ", f"حدث خطأ أثناء حفظ التوجيهات العامة: {str(e)}", "error")

            # إضافة زر اختيار صورة من المتصفح
            select_image_button = QPushButton("اختيار صورة من الجهاز")
            select_image_button.setFont(QFont("Calibri", 12, QFont.Bold))
            select_image_button.setStyleSheet("""
                QPushButton {
                    background-color: #3498db;
                    color: white;
                    border-radius: 5px;
                    padding: 10px;
                    min-width: 150px;
                    min-height: 40px;
                }
                QPushButton:hover {
                    background-color: #2980b9;
                }
            """)
            select_image_button.setCursor(Qt.PointingHandCursor)

            # دالة لاختيار صورة من المتصفح
            def select_image_action():
                success, image_path = select_image_from_browser()
                if success:
                    # حفظ النص في قاعدة البيانات أيضاً
                    try:
                        conn = sqlite3.connect(self.db_path)
                        cursor = conn.cursor()

                        cursor.execute("SELECT id FROM جدولة_الامتحان WHERE id = 1")
                        record = cursor.fetchone()

                        if record:
                            cursor.execute("UPDATE جدولة_الامتحان SET ملاحظات = ? WHERE id = 1", (instructions_text.toPlainText(),))
                        else:
                            cursor.execute("INSERT INTO جدولة_الامتحان (id, ملاحظات) VALUES (1, ?)", (instructions_text.toPlainText(),))

                        conn.commit()
                        conn.close()

                        self.show_message("نجاح", f"تم اختيار الصورة وحفظ النص بنجاح.\n\nمسار الصورة:\n{image_path}", "success")
                        instructions_dialog.accept()
                    except Exception as e:
                        self.show_message("خطأ", f"تم اختيار الصورة ولكن حدث خطأ في حفظ النص: {str(e)}", "error")

            select_image_button.clicked.connect(select_image_action)
            save_button.clicked.connect(save_instructions)

            # إضافة تسمية توضيحية
            options_label = QLabel("اختر طريقة إنشاء صورة التوجيهات:")
            options_label.setFont(QFont("Calibri", 12, QFont.Bold))
            options_label.setStyleSheet("""
                QLabel {
                    color: #2c3e50;
                    padding: 10px;
                    background-color: #ecf0f1;
                    border-radius: 5px;
                    border: 1px solid #bdc3c7;
                }
            """)
            options_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(options_label)

            # إضافة الأزرار إلى التخطيط
            button_layout = QHBoxLayout()
            button_layout.addStretch()
            button_layout.addWidget(select_image_button)
            button_layout.addSpacing(20)
            button_layout.addWidget(save_button)
            button_layout.addStretch()
            layout.addLayout(button_layout)

            # إضافة ملاحظة توضيحية
            note_label = QLabel("💡 يمكنك اختيار صورة جاهزة من جهازك أو إنشاء صورة من النص المكتوب أعلاه")
            note_label.setFont(QFont("Calibri", 10))
            note_label.setStyleSheet("""
                QLabel {
                    color: #7f8c8d;
                    padding: 5px;
                    font-style: italic;
                }
            """)
            note_label.setAlignment(Qt.AlignCenter)
            note_label.setWordWrap(True)
            layout.addWidget(note_label)

            # عرض النافذة
            instructions_dialog.exec_()
        except Exception as e:
            self.show_message("خطأ", f"حدث خطأ أثناء فتح نافذة التوجيهات العامة: {str(e)}", "error")
        finally:
            # إعادة عنوان النافذة الأصلي
            self.setWindowTitle("إدارة التقارير واللوائح والاستدعاءات")

    def collective_report(self):
        """إنشاء المحضر الجماعي للمترشحين"""
        try:
            # تعيين عنوان النافذة
            self.setWindowTitle("المحضر الجماعي للمترشحين - إدارة التقارير واللوائح والاستدعاءات")

            # التحقق من إدخال البيانات المطلوبة - استخدام العنوان5 بدلاً من العنوان2
            title5 = self.text_fields["العنوان5"].text().strip()

            if not title5:
                self.show_message("تنبيه", "الرجاء إدخال عنوان المحضر الجماعي على الأقل لاستخدامه كعنوان للمحضر الجماعي.", "warning")
                return

            # إنشاء شريط تقدم العملية محسن ومنسق
            progress = QProgressDialog("جاري إنشاء المحضر الجماعي للمترشحين...", "إلغاء", 0, 100, self)
            progress.setWindowTitle("إنشاء المحضر الجماعي")
            progress.setWindowModality(Qt.WindowModal)
            progress.setMinimumDuration(0)
            progress.setAutoClose(True)
            progress.setAutoReset(True)
            progress.setMinimumWidth(500)
            progress.setMinimumHeight(150)

            # تحسين مظهر شريط التقدم بشكل جميل ومنسق
            progress.setStyleSheet("""
                QProgressDialog {
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                               stop: 0 #f0f8ff, stop: 1 #e6f3ff);
                    border: 3px solid #1abc9c;
                    border-radius: 15px;
                    padding: 15px;
                    box-shadow: 0px 5px 15px rgba(0,0,0,0.3);
                }
                QLabel {
                    color: #2c3e50;
                    font-family: 'Calibri';
                    font-size: 13pt;
                    font-weight: bold;
                    background: transparent;
                    padding: 5px;
                }
                QProgressBar {
                    border: 2px solid #1abc9c;
                    border-radius: 8px;
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                               stop: 0 #ecf0f1, stop: 1 #bdc3c7);
                    text-align: center;
                    color: #2c3e50;
                    font-family: 'Calibri';
                    font-weight: bold;
                    font-size: 11pt;
                    min-height: 25px;
                    max-height: 25px;
                }
                QProgressBar::chunk {
                    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                               stop: 0 #1abc9c, stop: 0.5 #16a085, stop: 1 #1abc9c);
                    border-radius: 6px;
                    margin: 1px;
                }
                QPushButton {
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                               stop: 0 #dc3545, stop: 1 #c82333);
                    color: white;
                    border-radius: 12px;
                    padding: 12px 20px;
                    font-family: 'Segoe UI', 'Calibri';
                    font-weight: bold;
                    font-size: 12pt;
                    min-width: 140px;
                    border: 3px solid #c82333;
                }
                QPushButton:hover {
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                               stop: 0 #c82333, stop: 1 #bd2130);
                    border: 3px solid #dc3545;
                    transform: scale(1.05);
                }
                QPushButton:pressed {
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                               stop: 0 #bd2130, stop: 1 #a71e2a);
                }
            """)

            progress.setValue(0)
            progress.show()

            # محاولة استدعاء دالة إنشاء المحضر الجماعي من ملف print313.py
            try:
                # تحديث شريط التقدم - بدء العملية
                progress.setLabelText("🔄 جاري تحضير العملية...")
                progress.setValue(10)
                QApplication.processEvents()
                time.sleep(0.4)

                # تحديث شريط التقدم - تحميل الوحدة
                progress.setLabelText("📦 جاري تحميل وحدة إنشاء التقارير...")
                progress.setValue(20)
                QApplication.processEvents()
                time.sleep(0.3)

                # تحديث شريط التقدم - جلب البيانات
                progress.setLabelText("🗂️ جاري جلب بيانات المترشحين...")
                progress.setValue(30)
                QApplication.processEvents()
                time.sleep(0.4)

                # التحقق من وجود البيانات والحصول على الإحصائيات مسبقاً
                total_candidates = 0
                total_levels =  0
                gender_stats = {}
                age_stats = {}
                institution_stats = {}

                try:
                    conn = sqlite3.connect(self.db_path)
                    cursor = conn.cursor()

                    # عدد المترشحين الإجمالي
                    cursor.execute("SELECT COUNT(*) FROM امتحانات")
                    total_candidates = cursor.fetchone()[0]

                    if total_candidates == 0:
                        conn.close()
                        progress.close()
                        self.show_message("تنبيه", "لا توجد بيانات مترشحين في قاعدة البيانات.", "warning")
                        return

                    # عدد المستويات
                    cursor.execute("SELECT COUNT(DISTINCT المستوى) FROM امتحانات WHERE المستوى IS NOT NULL AND المستوى != ''")
                    total_levels = cursor.fetchone()[0]

                    # إحصائيات الجنس
                    cursor.execute("SELECT الجنس, COUNT(*) FROM امتحانات WHERE الجنس IS NOT NULL GROUP BY الجنس")
                    for row in cursor.fetchall():
                        gender = row[0] if row[0] and row[0].strip() else 'غير محدد'
                        gender_stats[gender] = row[1]

                    # إحصائيات العمر
                    cursor.execute("SELECT تاريخ_الازدياد FROM امتحانات WHERE تاريخ_الازدياد IS NOT NULL")
                    birth_dates = cursor.fetchall()

                    for birth_date_row in birth_dates:
                        birth_date_str = birth_date_row[0]
                        age = self.calculate_age(birth_date_str)
                        age_display = str(age) if age != 999 else 'غير محدد'
                        age_stats[age_display] = age_stats.get(age_display, 0) + 1

                    # ترتيب الأعمار من الأصغر للأكبر
                    sorted_age_stats = {}
                    numeric_ages = []
                    non_numeric_ages = {}

                    for age_str, count in age_stats.items():
                        if age_str != 'غير محدد':
                            try:
                                numeric_ages.append((int(age_str), count))
                            except ValueError:
                                non_numeric_ages[age_str] = count
                        else:
                            non_numeric_ages[age_str] = count

                    # ترتيب الأعمار الرقمية
                    numeric_ages.sort(key=lambda x: x[0])
                    for age, count in numeric_ages:
                        sorted_age_stats[str(age)] = count

                    # إضافة غير المحددة في النهاية
                    for age_str, count in non_numeric_ages.items():
                        sorted_age_stats[age_str] = count

                    age_stats = sorted_age_stats

                    # إحصائيات المؤسسة الأصلية
                    cursor.execute("SELECT \"المؤسسة_الأصلية\", COUNT(*) FROM امتحانات WHERE \"المؤسسة_الأصلية\" IS NOT NULL GROUP BY \"المؤسسة_الأصلية\"")
                    for row in cursor.fetchall():
                        institution = row[0] if row[0] and row[0].strip() else 'غير محدد'
                        institution_stats[institution] = row[1]

                    conn.close()
                except Exception as e:
                    print(f"خطأ في الحصول على الإحصائيات: {e}")
                    if 'conn' in locals():
                        conn.close()

                # تحديث شريط التقدم - معالجة البيانات
                progress.setLabelText("⚙️ جاري معالجة وتجميع البيانات...")
                progress.setValue(45)
                QApplication.processEvents()
                time.sleep(0.5)

                # تحديث شريط التقدم - إنشاء التقرير
                progress.setLabelText("📄 جاري إنشاء ملف PDF مع الإحصائيات...")
                progress.setValue(60)
                QApplication.processEvents()
                time.sleep(0.4)

                # استيراد ملف print313.py أو إنشاء التقرير محلياً
                success = False
                output_path = ""

                try:
                    import print313

                    # تحديث تقدم إنشاء التقرير
                    progress.setLabelText("📊 جاري إنشاء التقرير مع الإحصائيات المفصلة...")
                    progress.setValue(70)
                    QApplication.processEvents()

                    # استدعاء دالة إنشاء التقرير مع تمرير العنوان الصحيح من العنوان5
                    success, output_path, message = print313.print_exams_report(
                        parent=self,
                        level=None,  # جميع المستويات
                        report_title=title5,  # استخدام العنوان5 بدلاً من title2
                        subject_data=None
                    )

                    # التأكد من إنشاء التقرير بنجاح قبل تطبيق الخلفية
                    if success and output_path:
                        print("✅ تم إنشاء المحضر الجماعي الأساسي بنجاح")
                        print(f"📁 مسار التقرير: {output_path}")
                        
                        # تأخير قصير للتأكد من حفظ الملف بالكامل
                        progress.setLabelText("⏳ جاري الانتظار لضمان حفظ التقرير...")
                        progress.setValue(75)
                        QApplication.processEvents()
                        time.sleep(1)  # تأخير ثانية واحدة
                        
                        # التحقق من وجود الملف فعلياً
                        if os.path.exists(output_path):
                            print(f"✅ تم التأكد من وجود الملف: {output_path}")
                            
                            # التحقق من صحة ملف التقرير
                            if self.verify_report_file(output_path):
                                print("✅ ملف التقرير صالح ومقروء")
                                
                                # محاولة تطبيق الخلفية المحفوظة من جدول بيانات_المؤسسة
                                print("🎨 بدء البحث عن خلفية محفوظة لتطبيقها على المحضر الجماعي...")
                                
                                # البحث عن الخلفية المحفوظة
                                background_path = self.get_background_path()
                                background_applied = False
                                
                                if background_path:
                                    print(f"🔍 تم العثور على مسار خلفية: {background_path}")
                                    
                                    # اختبار الخلفية أولاً
                                    if self.test_background_before_applying(background_path):
                                        # تحديث شريط التقدم - تطبيق الخلفية
                                        progress.setLabelText("🎨 جاري تطبيق الخلفية المحسنة على المحضر الجماعي...")
                                        progress.setValue(85)
                                        QApplication.processEvents()
                                        time.sleep(0.3)
                                        
                                        print(f"📁 بدء تطبيق الخلفية من: {background_path}")
                                        
                                        try:
                                            # تأكد من أن مسار الخلفية محفوظ في الذاكرة
                                            self.background_pdf_path = background_path
                                            
                                            # تطبيق الخلفية باستخدام الطريقة المحسنة المُحدثة
                                            background_applied = self.apply_background_to_existing_pdf(output_path, background_path)
                                            
                                            if background_applied:
                                                print("🎉 تم تطبيق الخلفية على المحضر الجماعي بنجاح!")
                                                
                                                # التحقق من النتيجة النهائية
                                                if os.path.exists(output_path):
                                                    file_size = os.path.getsize(output_path)
                                                    print(f"📊 حجم الملف النهائي: {file_size} بايت")
                                                else:
                                                    print("⚠️ الملف النهائي غير موجود!")
                                            else:
                                                print("⚠️ فشل في تطبيق الخلفية على المحضر الجماعي")
                                                
                                        except Exception as bg_error:
                                            print(f"❌ خطأ في تطبيق الخلفية على المحضر الجماعي: {bg_error}")
                                            import traceback
                                            traceback.print_exc()
                                            background_applied = False
                                    else:
                                        print("❌ اختبار الخلفية فشل - لن يتم تطبيقها")
                                else:
                                    print("ℹ️ لا توجد خلفية محفوظة - سيتم إنشاء المحضر الجماعي بدون خلفية")
                                
                                # تحديث شريط التقدم - التحقق من سلامة الملف النهائي
                                progress.setLabelText("🔍 جاري التحقق من سلامة الملف النهائي...")
                                progress.setValue(90)
                                QApplication.processEvents()
                                time.sleep(0.3)
                                
                                # التحقق من سلامة الملف النهائي قبل فتحه
                                final_file_valid = False
                                try:
                                    if self.verify_report_file(output_path):
                                        print("✅ الملف النهائي صالح وجاهز للفتح")
                                        final_file_valid = True
                                    else:
                                        print("❌ الملف النهائي تالف أو غير قابل للقراءة")
                                except Exception as verify_error:
                                    print(f"⚠️ خطأ في التحقق من الملف النهائي: {verify_error}")
                                
                                # فتح الملف النهائي فقط إذا كان صالحاً
                                if final_file_valid:
                                    progress.setLabelText("📂 جاري فتح المحضر الجماعي...")
                                    progress.setValue(95)
                                    QApplication.processEvents()
                                    
                                    try:
                                        if sys.platform == 'win32':
                                            os.startfile(output_path)
                                        elif sys.platform == 'darwin':  # macOS
                                            subprocess.call(['open', output_path])
                                        else:  # Linux
                                            subprocess.call(['xdg-open', output_path])
                                        
                                        if background_applied:
                                            print("🎉 تم فتح المحضر الجماعي مع الخلفية بنجاح!")
                                        else:
                                            print("📄 تم فتح المحضر الجماعي بدون خلفية")
                                            
                                    except Exception as open_error:
                                        print(f"⚠️ تعذر فتح الملف: {open_error}")
                                        self.show_message("تنبيه", f"تم إنشاء التقرير ولكن تعذر فتح الملف: {str(open_error)}", "warning")
                                else:
                                    print("❌ لن يتم فتح الملف لأنه تالف")
                                    self.show_message("خطأ", "تم إنشاء التقرير ولكنه تالف. يرجى المحاولة مرة أخرى بدون خلفية.", "error")
                                    
                            else:
                                print("❌ ملف التقرير تالف أو غير مقروء - لن يتم تطبيق الخلفية")
                        else:
                            print(f"❌ لم يتم العثور على الملف المُنشأ: {output_path}")
                    else:
                        print(f"❌ فشل في إنشاء التقرير: {message}")

                except ImportError:
                    # في حالة عدم وجود print313.py، نشتاء التقرير باستخدام print313 مبسط
                    progress.setLabelText("📋 جاري إنشاء تقرير مبسط...")
                    progress.setValue(70)
                    QApplication.processEvents()

                    try:
                        from datetime import datetime

                        # إنشاء مجلد التقارير
                        downloads_folder = get_downloads_folder()
                        reports_folder = os.path.join(downloads_folder, "تقارير برنامج المعين في الحراسة العامة")
                        os.makedirs(reports_folder, exist_ok=True)

                        # إنشاء اسم ملف مع طابع زمني
                        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                        output_filename = f"المحضر_الجماعي_{timestamp}.pdf"
                        output_path = os.path.join(reports_folder, output_filename)

                        # هنا يمكن إضافة كود إنشاء PDF مبسط إذا لزم الأمر
                        success = True

                    except Exception as e:
                        print(f"خطأ في إنشاء التقرير المبسط: {e}")
                        success = False

                # تحديث شريط التقدم - اكتمال العملية
                progress.setLabelText("🎉 اكتملت العملية بنجاح!")
                progress.setValue(100)
                QApplication.processEvents()
                time.sleep(0.5)

                if success:
                    # تحديد رسالة النجاح بناءً على ما تم تطبيقه
                    success_message = f"تم إنشاء المحضر الجماعي للمترشحين بنجاح"
                    
                    # إضافة معلومة عن الخلفية إذا كانت متاحة
                    background_path = self.get_background_path()
                    if background_path:
                        success_message += " مع تطبيق الخلفية المحفوظة"
                    else:
                        success_message += " (بدون خلفية - لم يتم تحديد خلفية مسبقاً)"
                    
                    success_message += f".\n\n📊 ملخص التقرير:\n"
                    success_message += f"• العنوان المستخدم: {title5}\n"
                    success_message += f"• جميع المستويات ({total_levels} مستوى)\n"
                    success_message += f"• إجمالي المترشحين: {total_candidates}\n\n"

                    # إضافة إحصائيات الجنس
                    if gender_stats:
                        success_message += f"📈 إحصائيات الجنس:\n"
                        for gender, count in gender_stats.items():
                            percentage = (count / total_candidates * 100) if total_candidates > 0 else 0
                            success_message += f"  • {gender}: {count} ({percentage:.1f}%)\n"
                        success_message += "\n"

                    # إضافة إحصائيات العمر (أول 5 أعمار فقط لعدم إطالة الرسالة)
                    if age_stats:
                        success_message += f"🎂 إحصائيات العمر (عينة):\n"
                        count_displayed = 0
                        for age, count in age_stats.items():
                            if count_displayed >= 5:
                                success_message += f"  • ... والمزيد\n"
                                break
                            percentage = (count / total_candidates * 100) if total_candidates > 0 else 0
                            success_message += f"  • {age} سنة: {count} ({percentage:.1f}%)\n"
                            count_displayed += 1
                        success_message += "\n"

                    # إضافة إحصائيات المؤسسة (أول 3 مؤسسات فقط)
                    if institution_stats:
                        success_message += f"🏫 إحصائيات المؤسسات (عينة):\n"
                        count_displayed = 0
                        for institution, count in institution_stats.items():
                            if count_displayed >= 3:
                                success_message += f"  • ... والمزيد\n"
                                break
                            percentage = (count / total_candidates * 100) if total_candidates > 0 else 0
                            success_message += f"  • {institution}: {count} ({percentage:.1f}%)\n"
                            count_displayed += 1
                        success_message += "\n"

                    success_message += f"• يتضمن التقرير إحصائيات مفصلة لكل مستوى\n"
                    success_message += f"\n📁 مسار الملف:\n{output_path}"

                    self.show_message("نجاح", success_message, "success")
                else:
                    self.show_message("تنبيه", f"لم يتم إنشاء المحضر الجماعي: {message}", "warning")

            except Exception as e:
                self.show_message("خطأ", f"حدث خطأ أثناء إنشاء المحضر الجماعي للمترشحين: {str(e)}", "error")
            finally:
                # إغلاق شريط التقدم
                progress.close()

        except Exception as e:
            self.show_message("خطأ", f"حدث خطأ أثناء إنشاء المحضر الجماعي للمترشحين: {str(e)}", "error")
        finally:
            # إعادة عنوان النافذة الأصلي
            self.setWindowTitle("إدارة التقارير واللوائح والاستدعاءات")

    def calculate_age(self, birth_date_str):
        """حساب العمر من تاريخ الميلاد"""
        try:
            from datetime import datetime
            if not birth_date_str or birth_date_str.strip() == '':
                return 999  # قيمة كبيرة للتصنيف في النهاية

            # محاولة تحليل التاريخ بعدة تنسيقات
            formats = ['%Y-%m-%d', '%d/%m/%Y', '%d-%m-%Y', '%Y/%m/%d']
            birth_date = None

            for fmt in formats:
                try:
                    birth_date = datetime.strptime(birth_date_str.strip(), fmt)
                    break
                except ValueError:
                    continue

            if birth_date is None:
                return 999

            today = datetime.today()
            age = today.year - birth_date.year - ((today.month, today.day) < (birth_date.month, birth_date.day))

            return age
        except:
            return 999

    def clear_fields(self):
        """مسح جميع الحقول"""
        for field in self.text_fields.values():
            field.clear()

    def save_changes(self):
        """حفظ التغييرات في قاعدة البيانات"""
        try:
            # التحقق من إدخال البيانات المطلوبة
            title1 = self.text_fields["العنوان1"].text().strip()
            title2 = self.text_fields["العنوان2"].text().strip()
            title3 = self.text_fields["العنوان3"].text().strip()
            title4 = self.text_fields["العنوان4"].text().strip()
            title5 = self.text_fields["العنوان5"].text().strip()

            if not title1 and not title2 and not title3 and not title4 and not title5:
                self.show_message("تنبيه", "لا توجد بيانات للحفظ.", "warning")
                return

            # إنشاء نافذة تأكيد مخصصة
            confirm_dialog = CustomMessageDialog(
                self,
                "تأكيد الحفظ",
                "هل أنت متأكد من حفظ التغييرات؟",
                "info"
            )

            if confirm_dialog.exec_() == QDialog.Accepted:
                # تحديث السجل في قاعدة البيانات
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()

                # التحقق من وجود السجل (نبحث عن أول سجل)
                cursor.execute("SELECT * FROM جدول_الامتحان LIMIT 1")
                record = cursor.fetchone()

                if record:
                    # تحديث السجل الموجود - حفظ العناوين فقط
                    cursor.execute("""
                        UPDATE جدول_الامتحان
                        SET العنوان1 = ?, العنوان2 = ?, العنوان3 = ?, العنوان4 = ?, العنوان5 = ?
                        WHERE id = ?
                    """, (title1, title2, title3, title4, title5, record[0]))
                    conn.commit()
                    print("✅ تم حفظ العناوين في قاعدة البيانات بنجاح")
                    self.show_message("نجاح", "تم حفظ التغييرات بنجاح.", "success")
                else:
                    # إضافة سجل جديد - العناوين فقط
                    cursor.execute("""
                        INSERT INTO جدول_الامتحان (العنوان1, العنوان2, العنوان3, العنوان4, العنوان5)
                        VALUES (?, ?, ?, ?, ?)
                    """, (title1, title2, title3, title4, title5))
                    conn.commit()
                    self.show_message("نجاح", "تم إضافة العناوين بنجاح.", "success")

                conn.close()
        except Exception as e:
            self.show_message("خطأ", f"حدث خطأ أثناء حفظ التغييرات: {str(e)}", "error")

    def add_background(self):
        """إضافة خلفية PDF للتقارير"""
        try:
            # تعيين عنوان النافذة
            self.setWindowTitle("إضافة خلفية PDF - إدارة التقارير واللوائح والاستدعاءات")

            # فتح متصفح ملفات PDF
            file_dialog = QFileDialog(self)
            file_dialog.setWindowTitle("اختيار ملف PDF للخلفية")
            file_dialog.setNameFilter("ملفات PDF (*.pdf)")
            file_dialog.setFileMode(QFileDialog.ExistingFile)
            file_dialog.setAcceptMode(QFileDialog.AcceptOpen)
            file_dialog.setLayoutDirection(Qt.RightToLeft)

            # تطبيق تنسيق مخصص لمتصفح الملفات
            file_dialog.setStyleSheet("""
                QFileDialog {
                    background-color: #f0f8ff;
                    color: black;
                    font-family: Calibri;
                    font-size: 12pt;
                }
                QFileDialog QListView {
                    background-color: white;
                    border: 1px solid #3498db;
                    border-radius: 5px;
                }
                QFileDialog QPushButton {
                    background-color: #3498db;
                    color: white;
                    border-radius: 5px;
                    padding: 8px 15px;
                    font-weight: bold;
                    min-height: 30px;
                    min-width: 100px;
                }
                QFileDialog QPushButton:hover {
                    background-color: #2980b9;
                }
            """)

            if file_dialog.exec_() == QFileDialog.Accepted:
                selected_files = file_dialog.selectedFiles()
                if selected_files:
                    pdf_path = selected_files[0]
                    print(f"🎯 تم اختيار ملف الخلفية: {pdf_path}")

                    # التحقق من أن الملف هو PDF صالح
                    if not pdf_path.lower().endswith('.pdf'):
                        print("❌ الملف المختار ليس PDF")
                        self.show_message("خطأ", "الرجاء اختيار ملف PDF صالح.", "error")
                        return

                    # التحقق من وجود الملف
                    if not os.path.exists(pdf_path):
                        print(f"❌ الملف غير موجود: {pdf_path}")
                        self.show_message("خطأ", "الملف المحدد غير موجود.", "error")
                        return

                    print("✅ الملف صالح، جاري حفظ مسار الخلفية...")
                    # حفظ مسار الملف في الذاكرة
                    self.background_pdf_path = pdf_path
                    print(f"💾 تم حفظ مسار الخلفية في الذاكرة: {self.background_pdf_path}")

                    # حفظ مسار الخلفية في قاعدة البيانات
                    try:
                        print("🗄️ جاري حفظ مسار الخلفية في قاعدة البيانات...")
                        conn = sqlite3.connect(self.db_path)
                        cursor = conn.cursor()
                        
                        # التحقق من وجود عمود ImagePath2 في جدول بيانات_المؤسسة
                        cursor.execute("PRAGMA table_info(بيانات_المؤسسة)")
                        columns = [column[1] for column in cursor.fetchall()]
                        
                        if 'ImagePath2' not in columns:
                            print("🔧 إضافة عمود ImagePath2 إلى جدول بيانات_المؤسسة...")
                            cursor.execute("ALTER TABLE بيانات_المؤسسة ADD COLUMN ImagePath2 TEXT")
                        
                        # التحقق من وجود سجل في جدول بيانات_المؤسسة
                        cursor.execute("SELECT COUNT(*) FROM بيانات_المؤسسة")
                        record_count = cursor.fetchone()[0]
                        
                        if record_count > 0:
                            # تحديث السجل الموجود
                            cursor.execute("UPDATE بيانات_المؤسسة SET ImagePath2 = ? WHERE rowid = 1", (pdf_path,))
                            print("✅ تم تحديث مسار الخلفية في السجل الموجود")
                        else:
                            # إنشاء سجل جديد
                            cursor.execute("INSERT INTO بيانات_المؤسسة (ImagePath2) VALUES (?)", (pdf_path,))
                            print("✅ تم إنشاء سجل جديد مع مسار الخلفية")
                        
                        conn.commit()
                        conn.close()
                        print(f"💾 تم حفظ مسار الخلفية في قاعدة البيانات: {pdf_path}")
                        
                    except Exception as db_error:
                        print(f"⚠️ خطأ في حفظ مسار الخلفية في قاعدة البيانات: {db_error}")

                    # إنشاء تقرير تجريبي لمعاينة النتيجة
                    print("🔄 بدء إنشاء التقرير التجريبي...")
                    self.create_test_report_with_background(pdf_path)
                else:
                    print("⚠️ لم يتم اختيار أي ملف")
            else:
                print("❌ تم إلغاء اختيار الملف")

        except Exception as e:
            self.show_message("خطأ", f"حدث خطأ أثناء إضافة الخلفية: {str(e)}", "error")
        finally:
            # إعادة عنوان النافذة الأصلي
            self.setWindowTitle("إدارة التقارير واللوائح والاستدعاءات")

    def create_test_report_with_background(self, background_pdf_path):
        """إنشاء تقرير تجريبي مع الخلفية لمعاينة النتيجة"""
        try:
            # إنشاء شريط تقدم
            progress = QProgressDialog("جاري إنشاء تقرير تجريبي مع الخلفية...", "إلغاء", 0, 100, self)
            progress.setWindowTitle("إنشاء تقرير تجريبي")
            progress.setWindowModality(Qt.WindowModal)
            progress.setMinimumDuration(0)
            progress.setAutoClose(True)
            progress.setAutoReset(True)
            progress.setMinimumWidth(400)

            # تحسين مظهر شريط التقدم
            progress.setStyleSheet("""
                QProgressDialog {
                    background-color: #f0f8ff;
                    border: 2px solid #e67e22;
                    border-radius: 10px;
                    padding: 10px;
                }
                QLabel {
                    color: #d35400;
                    font-family: Calibri;
                    font-size: 12pt;
                    font-weight: bold;
                }
                QProgressBar {
                    border: 1px solid #e67e22;
                    border-radius: 5px;
                    background-color: #ecf0f1;
                    text-align: center;
                    color: black;
                    font-family: Calibri;
                    font-weight: bold;
                    min-height: 20px;
                }
                QProgressBar::chunk {
                    background-color: #e67e22;
                    width: 10px;
                    margin: 0.5px;
                }
                QPushButton {
                    background-color: #e74c3c;
                    color: white;
                    border-radius: 5px;
                    padding: 5px 10px;
                    font-family: Calibri;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #c0392b;
                }
            """)

            progress.setValue(0)
            progress.show()

            # تحديث شريط التقدم
            progress.setLabelText("جاري تحضير البيانات...")
            progress.setValue(20)
            QApplication.processEvents()
            time.sleep(0.3)

            # إنشاء تقرير تجريبي باستخدام الوحدة الجديدة
            progress.setLabelText("جاري إنشاء التقرير مع الخلفية...")
            progress.setValue(50)
            QApplication.processEvents()

            # الحصول على مجلد التنزيلات
            downloads_folder = get_downloads_folder()
            reports_folder = os.path.join(downloads_folder, "تقارير برنامج المعين في الحراسة العامة")
            os.makedirs(reports_folder, exist_ok=True)

            # إنشاء اسم الملف
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_path = os.path.join(reports_folder, f"تقرير_تجريبي_مع_خلفية_{timestamp}.pdf")

            # إنشاء تقرير تجريبي بسيط مع الخلفية
            success = self.create_simple_test_report(output_path, background_pdf_path)

            progress.setLabelText("جاري فتح التقرير...")
            progress.setValue(80)
            QApplication.processEvents()
            time.sleep(0.3)

            progress.setValue(100)
            QApplication.processEvents()
            time.sleep(0.5)

            if success:
                # فتح الملف
                try:
                    if sys.platform == 'win32':
                        os.startfile(output_path)
                    elif sys.platform == 'darwin':  # macOS
                        subprocess.call(['open', output_path])
                    else:  # Linux
                        subprocess.call(['xdg-open', output_path])
                except Exception as e:
                    print(f"خطأ في فتح الملف: {e}")

                self.show_message(
                    "نجح إضافة الخلفية!",
                    f"تم إنشاء تقرير تجريبي مع الخلفية بنجاح!\n\n"
                    f"📁 مسار الملف:\n{output_path}\n\n"
                    f"🎨 مسار ملف الخلفية:\n{background_pdf_path}\n\n"
                    f"💡 ملاحظة: سيتم استخدام هذه الخلفية في جميع التقارير المستقبلية.\n"
                    f"لا تنس حفظ التغييرات باستخدام زر 'حفظ'.",
                    "success"
                )
            else:
                self.show_message("خطأ",
                    "فشل في إنشاء التقرير التجريبي.\n\n"
                    "تأكد من:\n"
                    "1. تثبيت المكتبات المطلوبة\n"
                    "2. صحة ملف الخلفية المختار\n"
                    "3. وجود مساحة كافية على القرص\n\n"
                    "لتثبيت المكتبات، قم بتشغيل:\n"
                    "pip install -r requirements.txt",
                    "error")

        except Exception as e:
            self.show_message("خطأ", f"حدث خطأ أثناء إنشاء التقرير التجريبي: {str(e)}", "error")

    def create_simple_test_report(self, output_path, background_pdf_path):
        """إنشاء تقرير تجريبي بسيط مع خلفية PDF - الطريقة الصحيحة"""
        try:
            from PyPDF2 import PdfWriter, PdfReader
            from reportlab.pdfgen import canvas
            from reportlab.pdfbase import pdfmetrics
            from reportlab.pdfbase.ttfonts import TTFont
            import arabic_reshaper
            from bidi.algorithm import get_display

            print("🔄 إنشاء تقرير مع خلفية PDF باستخدام الطريقة الصحيحة...")

            # قراءة الصفحة الأولى من PDF الخلفية
            reader_bg = PdfReader(background_pdf_path)
            bg_page = reader_bg.pages[0]

            # أبعاد الصفحة بوحدة النقطة (1 نقطة = 1/72 بوصة)
            mediabox = bg_page.mediabox
            width, height = float(mediabox.width), float(mediabox.height)

            print(f"📏 أبعاد الصفحة: {width} x {height} نقطة")

            # إنشاء طبقة نصية مؤقتة في الذاكرة
            packet = io.BytesIO()
            c = canvas.Canvas(packet, pagesize=(width, height))

            # محاولة تحميل خط عربي
            try:
                fonts_dir = os.path.join(os.path.dirname(__file__), 'fonts')
                if os.path.exists(os.path.join(fonts_dir, 'arial.ttf')):
                    pdfmetrics.registerFont(TTFont('Arabic', os.path.join(fonts_dir, 'arial.ttf')))
                    font_name = 'Arabic'
                else:
                    font_name = 'Helvetica'
            except:
                font_name = 'Helvetica'

            # دالة لتحويل النص العربي
            def ar_text(text):
                try:
                    reshaped = arabic_reshaper.reshape(str(text))
                    return get_display(reshaped)
                except:
                    return str(text)

            # إضافة نص فوق الخلفية
            c.setFillColorRGB(0, 0, 0)  # نص أسود
            c.setFont(font_name, 24)

            # العنوان الرئيسي
            title = ar_text("✅ تم دمج الخلفية بنجاح!")
            c.drawCentredString(width/2, height - 100, title)

            # النص التوضيحي
            c.setFont(font_name, 16)
            content_lines = [
                "هذا النص يظهر فوق الخلفية المختارة",
                "يمكنك رؤية الخلفية خلف هذا النص بوضوح",
                "سيتم استخدام هذه الخلفية في جميع التقارير المستقبلية",
                "",
                f"تاريخ الإنشاء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                "برنامج المعين في الحراسة العامة"
            ]

            y_position = height - 180
            for line in content_lines:
                if line:
                    text = ar_text(line)
                    c.drawCentredString(width/2, y_position, text)
                y_position -= 35

            # إضافة مربع ملون للتأكيد
            c.setFillColorRGB(1, 1, 0, alpha=0.7)  # أصفر شفاف
            c.setStrokeColorRGB(0, 0, 0)  # إطار أسود
            c.setLineWidth(2)
            c.rect(80, height - 450, width - 160, 100, fill=1, stroke=1)

            # نص داخل المربع
            c.setFillColorRGB(0, 0, 0)  # نص أسود
            c.setFont(font_name, 18)
            box_text = ar_text("🎉 الخلفية تعمل بشكل مثالي!")
            c.drawCentredString(width/2, height - 400, box_text)

            # إنهاء الصفحة وحفظ في الذاكرة
            c.showPage()
            c.save()
            packet.seek(0)

            # دمج الطبقة النصية مع صفحة الخلفية
            reader_overlay = PdfReader(packet)
            overlay_page = reader_overlay.pages[0]

            # دمج الطبقات - النص فوق الخلفية
            bg_page.merge_page(overlay_page)

            # إنشاء كاتب PDF وحفظ النتيجة
            writer = PdfWriter()
            writer.add_page(bg_page)

            # حفظ الـ PDF الناتج
            with open(output_path, "wb") as f:
                writer.write(f)

            print("✅ تم دمج الخلفية بنجاح باستخدام الطريقة الصحيحة!")
            return True

        except ImportError as e:
            print(f"⚠️ مكتبة مطلوبة غير متوفرة: {e}")
            return False

        except Exception as e:
            print(f"❌ خطأ في دمج الخلفية: {e}")
            import traceback
            traceback.print_exc()
            return False

    def apply_background_to_existing_pdf(self, input_pdf_path, background_pdf_path):
        """تطبيق خلفية على PDF موجود - طريقة محسنة مع منع تراكم الطبقات"""
        try:
            from PyPDF2 import PdfWriter, PdfReader
            import copy
            
            print("🔄 بدء تطبيق الخلفية على PDF موجود...")
            print(f"📁 ملف التقرير: {input_pdf_path}")
            print(f"🎨 ملف الخلفية: {background_pdf_path}")

            # قراءة ملف الخلفية
            background_reader = PdfReader(background_pdf_path)
            if len(background_reader.pages) == 0:
                print("❌ ملف الخلفية فارغ أو تالف")
                return False
            
            original_background_page = background_reader.pages[0]
            print(f"📐 أبعاد صفحة الخلفية: {original_background_page.mediabox}")

            # قراءة ملف التقرير الأصلي
            report_reader = PdfReader(input_pdf_path)
            if len(report_reader.pages) == 0:
                print("❌ ملف التقرير فارغ أو تالف")
                return False
                
            print(f"📄 عدد صفحات التقرير: {len(report_reader.pages)}")

            # إنشاء كاتب PDF جديد
            writer = PdfWriter()

            # تطبيق الخلفية على كل صفحة مع منع تراكم الطبقات
            for page_num, report_page in enumerate(report_reader.pages):
                print(f"   🔄 معالجة الصفحة {page_num + 1}...")
                
                try:
                    # ⭐ الحل الصحيح: إنشاء نسخة جديدة من الخلفية لكل صفحة
                    # إعادة قراءة صفحة الخلفية من المصدر الأصلي لتجنب التراكم
                    fresh_background_reader = PdfReader(background_pdf_path)
                    fresh_background_page = fresh_background_reader.pages[0]
                    
                    # دمج محتوى التقرير فوق النسخة الجديدة من الخلفية
                    fresh_background_page.merge_page(report_page)
                    
                    # إضافة الصفحة المدموجة للكاتب
                    writer.add_page(fresh_background_page)
                    
                    print(f"   ✅ تم دمج الصفحة {page_num + 1} بدون تراكم")
                    
                except Exception as page_error:
                    print(f"   ⚠️ خطأ في الصفحة {page_num + 1}: {page_error}")
                    
                    # محاولة طريقة بديلة: نسخ عميق
                    try:
                        print(f"   🔄 محاولة نسخ عميق للصفحة {page_num + 1}...")
                        
                        # طريقة بديلة: استخدام نسخ عميق
                        background_copy = copy.deepcopy(original_background_page)
                        background_copy.merge_page(report_page)
                        
                        writer.add_page(background_copy)
                        print(f"   ✅ نجح النسخ العميق للصفحة {page_num + 1}")
                        
                    except Exception as fallback_error:
                        print(f"   ❌ فشلت جميع الطرق للصفحة {page_num + 1}: {fallback_error}")
                        # إضافة الصفحة الأصلية بدون خلفية كحل أخير
                        writer.add_page(report_page)
                        print(f"   📄 تم إضافة الصفحة {page_num + 1} بدون خلفية")

            # حفظ النتيجة في ملف مؤقت
            temp_file = input_pdf_path.replace('.pdf', '_with_background_temp.pdf')
            print(f"💾 حفظ النتيجة في: {temp_file}")
            
            with open(temp_file, 'wb') as output_file:
                writer.write(output_file)

            # التحقق من نجاح الحفظ
            if os.path.exists(temp_file) and os.path.getsize(temp_file) > 0:
                print("✅ تم حفظ الملف المؤقت بنجاح")
                
                # استبدال الملف الأصلي
                import shutil
                shutil.move(temp_file, input_pdf_path)
                print("🔄 تم استبدال الملف الأصلي")
                
                return True
            else:
                print("❌ فشل في حفظ الملف المؤقت")
                return False

        except Exception as e:
            print(f"❌ خطأ عام في تطبيق الخلفية: {e}")
            import traceback
            traceback.print_exc()
            return False

    def apply_background_to_pdf_like_template(self, input_pdf_path, background_pdf_path):
        """تطبيق خلفية PDF على ملف PDF موجود - بنفس طريقة النموذج التجريبي"""
        try:
            print("🔄 محاولة تطبيق الخلفية بالطريقة المحسنة...")
            
            # استخدام الطريقة المحسنة
            return self.apply_background_to_existing_pdf(input_pdf_path, background_pdf_path)

        except Exception as e:
            print(f"❌ خطأ في تطبيق الخلفية بطريقة النموذج: {e}")
            import traceback
            traceback.print_exc()
            return False

    def apply_background_to_pdf(self, input_pdf_path, output_pdf_path=None):
        """تطبيق خلفية PDF على ملف PDF موجود"""
        try:
            print(f"🚀 بدء تطبيق الخلفية على: {input_pdf_path}")
            
            # التحقق من وجود خلفية محفوظة
            if not hasattr(self, 'background_pdf_path') or not self.background_pdf_path:
                print("⚠️ لا توجد خلفية محفوظة في الذاكرة")
                return False

            print(f"📁 مسار الخلفية: {self.background_pdf_path}")
            
            if not os.path.exists(self.background_pdf_path):
                print(f"❌ ملف الخلفية غير موجود: {self.background_pdf_path}")
                return False

            if not os.path.exists(input_pdf_path):
                print(f"❌ ملف PDF المدخل غير موجود: {input_pdf_path}")
                return False

            # استخدام نفس الطريقة المستخدمة في النموذج التجريبي
            return self.apply_background_to_pdf_like_template(input_pdf_path, self.background_pdf_path)

        except Exception as e:
            print(f"❌ خطأ في تطبيق الخلفية: {e}")
            import traceback
            traceback.print_exc()
            return False

    def get_background_path(self):
        """الحصول على مسار خلفية PDF المحفوظة"""
        try:
            # التحقق من الذاكرة أولاً
            if hasattr(self, 'background_pdf_path') and self.background_pdf_path:
                if os.path.exists(self.background_pdf_path):
                    return self.background_pdf_path

            # محاولة قراءة المسار من جدول بيانات_المؤسسة
            print("🔍 البحث عن مسار الخلفية في جدول بيانات_المؤسسة...")
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT ImagePath2 FROM بيانات_المؤسسة LIMIT 1")
            result = cursor.fetchone()
            conn.close()

            if result and result[0] and os.path.exists(result[0]):
                print(f"✅ تم العثور على مسار الخلفية: {result[0]}")
                self.background_pdf_path = result[0]
                return result[0]
            else:
                print("⚠️ لم يتم العثور على مسار خلفية صالح في قاعدة البيانات")

            return None

        except Exception as e:
            print(f"❌ خطأ في الحصول على مسار الخلفية: {e}")
            return None

    def test_background_before_applying(self, background_path):
        """اختبار الخلفية قبل تطبيقها للتأكد من أنها تعمل"""
        try:
            from PyPDF2 import PdfReader
            
            print("🧪 اختبار الخلفية قبل التطبيق...")
            print(f"📁 مسار الخلفية: {background_path}")
            
            # التحقق من وجود الملف
            if not os.path.exists(background_path):
                print(f"❌ ملف الخلفية غير موجود: {background_path}")
                return False
                
            # التحقق من صحة الملف
            try:
                reader = PdfReader(background_path)
                if len(reader.pages) == 0:
                    print("❌ ملف الخلفية فارغ")
                    return False
                    
                page = reader.pages[0]
                print(f"✅ الخلفية صالحة - أبعاد الصفحة: {page.mediabox}")
                print(f"📐 العرض: {page.mediabox.width}, الارتفاع: {page.mediabox.height}")
                return True
                
            except Exception as pdf_error:
                print(f"❌ خطأ في قراءة ملف الخلفية: {pdf_error}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في اختبار الخلفية: {e}")
            return False

    def close_window(self):
        """إغلاق النافذة"""
        try:
            # إنشاء نافذة تأكيد مخصصة
            confirm_dialog = CustomMessageDialog(
                self,
                "تأكيد الإغلاق",
                "هل أنت متأكد من إغلاق النافذة؟\nسيتم فقدان أي بيانات غير محفوظة.",
                "warning"
            )

            if confirm_dialog.exec_() == QDialog.Accepted:
                self.accept()
        except Exception as e:
            self.show_message("خطأ", f"حدث خطأ أثناء إغلاق النافذة: {str(e)}", "error")

    def verify_report_file(self, report_path):
        """التحقق من صحة ملف التقرير المُنشأ"""
        try:
            print(f"🔍 التحقق من ملف التقرير: {report_path}")
            
            # التحقق من وجود الملف
            if not os.path.exists(report_path):
                print(f"❌ ملف التقرير غير موجود: {report_path}")
                return False
                
            # التحقق من حجم الملف
            file_size = os.path.getsize(report_path)
            print(f"📊 حجم ملف التقرير: {file_size} بايت")
            
            if file_size == 0:
                print("❌ ملف التقرير فارغ")
                return False
                
            # التحقق من صحة PDF
            try:
                from PyPDF2 import PdfReader
                reader = PdfReader(report_path)
                page_count = len(reader.pages)
                print(f"📄 عدد صفحات التقرير: {page_count}")
                
                if page_count == 0:
                    print("❌ التقرير لا يحتوي على صفحات")
                    return False
                    
                # التحقق من الصفحة الأولى
                first_page = reader.pages[0]
                print(f"📐 أبعاد الصفحة الأولى: {first_page.mediabox}")
                
                print("✅ ملف التقرير صالح ومقروء")
                return True
                
            except Exception as pdf_error:
                print(f"❌ خطأ في قراءة ملف التقرير كـ PDF: {pdf_error}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في التحقق من ملف التقرير: {e}")
            return False
# للاختبار المستقل
if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = Sub40Window()
    window.show()
    sys.exit(app.exec_())