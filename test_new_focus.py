#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار النافذة المحدثة مع التركيز على الهدفين المحددين
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt

# إضافة مسار المجلد الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    """اختبار النافذة مع المحتوى الجديد"""
    try:
        from sub26_window import ExamScheduleInstructions
        
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء النافذة
        window = ExamScheduleInstructions()
        window.show()
        
        print("🚀 تم فتح نافذة إعداد واستيراد جدولة الامتحان!")
        print("\n🎯 المحتوى الجديد يركز على هدفين أساسيين:")
        
        print("\n📋 الهدف الأول - إعداد جدولة الامتحان:")
        print("✅ الطريقة الأولى: إنشاء جدولة شخصية بأي برنامج")
        print("✅ الطريقة الثانية: استخدام النموذج المرفق مع البرنامج")
        print("✅ الطريقة الثالثة: استخدام النماذج الرسمية من الجهات المسؤولة")
        
        print("\n📄 الهدف الثاني - حفظ واستيراد كـ PDF:")
        print("✅ طرق حفظ الملف كـ PDF")
        print("✅ خطوات استيراد الجدولة للبرنامج")
        print("✅ توضيح أن الملف سيستخدم كخلفية لنماذج الاستدعاءات")
        
        print("\n🎨 الهدف النهائي:")
        print("استخدام ملف PDF المستورد كخلفية في نماذج استدعاء الطلاب")
        
        print("\n💡 التحديثات الرئيسية:")
        print("- إزالة الشرح التفصيلي لطريقة إنشاء الجدولة")
        print("- التركيز على حرية المستخدم في اختيار طريقة الإنشاء")
        print("- توضيح الهدف النهائي من استيراد الملف")
        print("- تبسيط التعليمات والتركيز على الأساسيات")
        
        print("\n🔧 الأزرار المتاحة:")
        print("- عرض الجدولة الحالية")
        print("- استيراد جدولة الامتحان (PDF)")
        print("- فتح مجلد الجدولة")
        print("- فتح نموذج استدعاء التلاميذ")
        print("- 🔍 (فحص قاعدة البيانات)")
        
        # تشغيل التطبيق
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل النافذة: {e}")

if __name__ == "__main__":
    main()
