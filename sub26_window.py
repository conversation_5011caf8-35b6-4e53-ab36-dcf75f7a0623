"""
نافذة تعليمات إعداد جدولة الامتحان
===============================

هذا الملف يحتوي على نافذة تعليمات تساعد المستخدم في إعداد جدولة الامتحان
بطريقة يدوية خارج البرنامج، ثم استيرادها كملف PDF للبرنامج.
"""

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QMessageBox, QTextEdit, QScrollArea, QFrame, QFileDialog
)
from PyQt5.QtGui import QFont, QPixmap
from PyQt5.QtCore import Qt
import os
import subprocess
import sqlite3
import shutil
from datetime import datetime
from datetime import datetime

class ExamScheduleInstructions(QDialog):
    """
    نافذة تعليمات إعداد جدولة الامتحان
    
    هذه النافذة تعرض للمستخدم تعليمات مفصلة حول كيفية إعداد جدولة الامتحان
    في برنامج Word، وتصديرها كملف PDF، ثم استيرادها للبرنامج.
    """

    def __init__(self, parent=None):
        super().__init__(parent)
        # إنشاء هيكل مجلد جدولة الامتحان
        self.create_schedule_folder_structure()
        self.initUI()

    def initUI(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("إعداد واستيراد جدولة الامتحان")
        self.setFixedSize(1000, 650)
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
            }
            QLabel {
                color: #2c3e50;
            }
        """)
        self.setLayoutDirection(Qt.RightToLeft)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)

        # العنوان الرئيسي
        title_label = QLabel("إعداد واستيراد جدولة الامتحان")
        title_label.setFont(QFont("Calibri", 18, QFont.Bold))
        title_label.setStyleSheet("""
            color: #e74c3c;
            background-color: #ffffff;
            padding: 15px;
            border: 2px solid #e74c3c;
            border-radius: 10px;
            margin-bottom: 10px;
        """)
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)

        # منطقة النص القابلة للتمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                background-color: #ffffff;
            }
        """)

        # إطار المحتوى
        content_frame = QFrame()
        content_layout = QVBoxLayout(content_frame)
        content_layout.setContentsMargins(20, 20, 20, 20)
        content_layout.setSpacing(15)

        # النص التوضيحي
        instructions_text = """
<style>
li {
    font-family: 'Calibri';
    font-size: 18px;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 5px;
}
p {
    font-family: 'Calibri';
    font-size: 18px;
    font-weight: bold;
    color: #2c3e50;
}
ul {
    font-family: 'Calibri';
    font-size: 18px;
    font-weight: bold;
    color: #2c3e50;
}
</style>
<div style="font-family: 'Calibri'; font-size: 12px; font-weight: bold; color: #2c3e50; line-height: 1.6;">

<h3 style="color: #e74c3c; border-bottom: 2px solid #e74c3c; padding-bottom: 5px;">🎯 الهدف من هذه النافذة:</h3>
<p style="background-color: #f8f9fa; padding: 15px; border-left: 4px solid #e74c3c; margin: 10px 0;">
هذه النافذة تساعدك في إعداد واستيراد جدولة الامتحان كملف PDF لاستخدامه كخلفية في نماذج الاستدعاءات التي سيقوم البرنامج بإنشائها.
</p>

<h3 style="color: #3498db; border-bottom: 2px solid #3498db; padding-bottom: 5px;">📋 الهدف الأول: إعداد جدولة الامتحان</h3>

<h4 style="color: #e67e22;">🔸 الطريقة الأولى - إنشاء جدولة شخصية:</h4>
<ul>
<li>قم بإنشاء جدولة الامتحان بالطريقة التي تناسبك وتناسب مؤسستك</li>
<li>استخدم أي برنامج تفضله (Microsoft Word, Excel, PowerPoint, أو أي برنامج آخر)</li>
<li>صمم الجدولة وفقاً لاحتياجاتك الخاصة (ألوان، خطوط، تخطيط، إلخ)</li>
<li>أضف شعار مؤسستك والمعلومات المطلوبة</li>
</ul>

<h4 style="color: #e67e22;">🔸 الطريقة الثانية - استخدام النموذج المرفق:</h4>
<ul>
<li>استخدم زر "فتح نموذج استدعاء التلاميذ" للوصول للنموذج الموجود في مجلد البرنامج</li>
<li>قم بتعديل النموذج حسب احتياجاتك</li>
<li>غير الألوان والخطوط والتخطيط كما تشاء</li>
<li>أضف أو احذف أي عناصر تراها مناسبة</li>
</ul>

<h4 style="color: #e67e22;">🔸 الطريقة الثالثة - استخدام النماذج الرسمية:</h4>
<ul>
<li>استخدم النماذج المرسلة إليك من الجهات المسؤولة عن الامتحانات</li>
<li>قم بتعبئة هذه النماذج بالمعلومات المطلوبة</li>
<li>تأكد من إضافة جميع البيانات الضرورية</li>
<li>احتفظ بالتنسيق الأصلي للنموذج الرسمي</li>
</ul>

<h3 style="color: #27ae60; border-bottom: 2px solid #27ae60; padding-bottom: 5px;">📄 الهدف الثاني: حفظ واستيراد الجدولة كـ PDF</h3>

<h4 style="color: #e67e22;">🔸 خطوات حفظ الجدولة كـ PDF:</h4>
<ul>
<li><strong>الطريقة الأولى - التصدير المباشر:</strong></li>
<ul style="margin-top: 5px; margin-bottom: 10px;">
<li>من قائمة "ملف" اختر "تصدير" → "إنشاء PDF/XPS"</li>
<li>أو استخدم "حفظ باسم" واختر نوع الملف PDF</li>
</ul>
<li><strong>الطريقة الثانية - الطباعة إلى PDF:</strong></li>
<ul style="margin-top: 5px; margin-bottom: 10px;">
<li>اضغط Ctrl+P أو "ملف" → "طباعة"</li>
<li>اختر "Microsoft Print to PDF" من قائمة الطابعات</li>
<li>اضغط "طباعة" واختر مكان الحفظ</li>
</ul>
<li>تأكد من جودة الملف ووضوح النص</li>
</ul>

<h4 style="color: #e67e22;">🔸 خطوات استيراد الجدولة للبرنامج:</h4>
<ul>
<li>استخدم زر "استيراد جدولة الامتحان (PDF)" أسفل هذه النافذة</li>
<li>اختر ملف PDF الذي أنشأته</li>
<li>سيقوم البرنامج بحفظ الملف في مجلد "جدولة_الامتحان"</li>
<li>سيتم حفظ مسار الملف في قاعدة البيانات</li>
<li><strong>سيستخدم البرنامج هذا الملف كخلفية لنماذج الاستدعاءات</strong></li>
</ul>

<h3 style="color: #9b59b6; border-bottom: 2px solid #9b59b6; padding-bottom: 5px;">🎨 الاستخدام في البرنامج:</h3>
<div style="background-color: #f4f1ff; padding: 15px; border-left: 4px solid #9b59b6; margin: 10px 0;">
<p><strong>ملاحظة مهمة:</strong> بعد استيراد ملف PDF للجدولة، سيقوم البرنامج باستخدامه كخلفية عند إنشاء نماذج استدعاء الطلاب. هذا يضمن أن جميع الاستدعاءات ستحمل نفس تصميم وتنسيق الجدولة التي أعددتها.</p>
</div>

<h3 style="color: #c0392b; border-bottom: 2px solid #c0392b; padding-bottom: 5px;">� استخدام الأزرار:</h3>
<ul>
<li><strong>عرض الجدولة الحالية:</strong> لمعاينة ملف الجدولة المحفوظ حالياً</li>
<li><strong>استيراد جدولة الامتحان (PDF):</strong> لاختيار وتحميل ملف PDF جديد للجدولة</li>
<li><strong>فتح مجلد الجدولة:</strong> لرؤية جميع الملفات المحفوظة</li>
<li><strong>فتح نموذج استدعاء التلاميذ:</strong> للوصول للنموذج المرفق مع البرنامج</li>
<li><strong>🔍:</strong> لفحص حالة قاعدة البيانات والملفات المحفوظة</li>
</ul>

<h3 style="color: #f39c12; border-bottom: 2px solid #f39c12; padding-bottom: 5px;">� نصائح هامة:</h3>
<ul>
<li>تأكد من جودة ووضوح ملف PDF قبل الاستيراد</li>
<li>استخدم أسماء واضحة ومفهومة للملفات</li>
<li>احتفظ بنسخ احتياطية من ملفاتك الأصلية</li>
<li>يمكنك تغيير الجدولة في أي وقت بتكرار عملية الاستيراد</li>
<li>تأكد من أن الجدولة تحتوي على جميع المعلومات المطلوبة</li>
</ul>

</div>
        """

        # إنشاء QTextEdit لعرض النص
        instructions_display = QTextEdit()
        instructions_display.setHtml(instructions_text)
        instructions_display.setReadOnly(True)
        instructions_display.setFont(QFont("Calibri", 16, QFont.Bold))
        instructions_display.setStyleSheet("""
            QTextEdit {
                border: none;
                background-color: transparent;
                padding: 10px;
                color: #2c3e50;
                font-weight: bold;
            }
        """)

        content_layout.addWidget(instructions_display)
        scroll_area.setWidget(content_frame)
        main_layout.addWidget(scroll_area)

        # منطقة الأزرار
        buttons_layout = QHBoxLayout()

        # زر عرض الجدولة الحالية
        view_current_button = QPushButton("عرض الجدولة الحالية")
        view_current_button.setFont(QFont("Calibri", 12, QFont.Bold))
        view_current_button.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border-radius: 8px;
                padding: 10px 20px;
                min-height: 35px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        view_current_button.clicked.connect(self.show_current_schedule_info)

        # زر استيراد جدولة الامتحان
        import_button = QPushButton("استيراد جدولة الامتحان (PDF)")
        import_button.setFont(QFont("Calibri", 12, QFont.Bold))
        import_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border-radius: 8px;
                padding: 10px 20px;
                min-height: 35px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        import_button.clicked.connect(self.import_pdf_schedule)

        # زر فتح مجلد جدولة الامتحان
        open_folder_button = QPushButton("فتح مجلد الجدولة")
        open_folder_button.setFont(QFont("Calibri", 12, QFont.Bold))
        open_folder_button.setStyleSheet("""
            QPushButton {
                background-color: #16a085;
                color: white;
                border-radius: 8px;
                padding: 10px 20px;
                min-height: 35px;
            }
            QPushButton:hover {
                background-color: #138d75;
            }
        """)
        open_folder_button.clicked.connect(self.open_reports_folder)

        # زر فتح نموذج Word
        open_word_button = QPushButton("فتح نموذج استدعاء التلاميذ")
        open_word_button.setFont(QFont("Calibri", 12, QFont.Bold))
        open_word_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border-radius: 8px;
                padding: 10px 20px;
                min-height: 35px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        open_word_button.clicked.connect(self.open_word)

        # زر إغلاق
        close_button = QPushButton("إغلاق")
        close_button.setFont(QFont("Calibri", 12, QFont.Bold))
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border-radius: 8px;
                padding: 10px 20px;
                min-height: 35px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        close_button.clicked.connect(self.accept)

        # زر اختبار قاعدة البيانات (مخفي في الأصل)
        test_db_button = QPushButton("🔍")
        test_db_button.setFont(QFont("Calibri", 10, QFont.Bold))
        test_db_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border-radius: 15px;
                padding: 5px;
                min-width: 30px;
                min-height: 30px;
                max-width: 30px;
                max-height: 30px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        test_db_button.setToolTip("اختبار قاعدة البيانات")
        test_db_button.clicked.connect(self.test_database_connection)

        # إضافة الأزرار
        buttons_layout.addStretch()
        buttons_layout.addWidget(view_current_button)
        buttons_layout.addSpacing(8)
        buttons_layout.addWidget(import_button)
        buttons_layout.addSpacing(8)
        buttons_layout.addWidget(open_folder_button)
        buttons_layout.addSpacing(8)
        buttons_layout.addWidget(open_word_button)
        buttons_layout.addSpacing(8)
        buttons_layout.addWidget(close_button)
        buttons_layout.addSpacing(5)
        buttons_layout.addWidget(test_db_button)
        buttons_layout.addStretch()

        main_layout.addLayout(buttons_layout)

        # التحقق من وجود جدولة محفوظة عند فتح النافذة
        self.check_existing_schedule()

    def import_pdf_schedule(self):
        """استيراد ملف PDF لجدولة الامتحان وحفظه في المجلد المخصص"""
        try:
            # فتح متصفح ملفات PDF
            file_dialog = QFileDialog()
            file_dialog.setWindowTitle("اختر ملف جدولة الامتحان (PDF)")
            file_dialog.setNameFilter("ملفات PDF (*.pdf)")
            file_dialog.setFileMode(QFileDialog.ExistingFile)
            file_dialog.setViewMode(QFileDialog.Detail)
            
            # تعيين المجلد الافتراضي (سطح المكتب)
            desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
            file_dialog.setDirectory(desktop_path)
            
            if file_dialog.exec_() == QFileDialog.Accepted:
                selected_files = file_dialog.selectedFiles()
                if selected_files:
                    source_file = selected_files[0]
                    
                    # التحقق من أن الملف هو PDF فعلاً
                    if not source_file.lower().endswith('.pdf'):
                        QMessageBox.warning(self, "خطأ", "يرجى اختيار ملف PDF فقط.")
                        return
                    
                    # إنشاء مجلد جدولة_الامتحان داخل مجلد البرنامج
                    program_dir = os.path.dirname(os.path.abspath(__file__))
                    schedule_folder = os.path.join(program_dir, "جدولة_الامتحان")
                    os.makedirs(schedule_folder, exist_ok=True)
                    
                    # تحديد اسم الملف الجديد
                    original_filename = os.path.basename(source_file)
                    destination_file = os.path.join(schedule_folder, original_filename)
                    
                    # إذا كان الملف موجوداً، اسأل المستخدم عن الاستبدال
                    if os.path.exists(destination_file):
                        reply = QMessageBox.question(self, "ملف موجود", 
                            f"الملف '{original_filename}' موجود بالفعل.\n"
                            "هل تريد استبداله؟",
                            QMessageBox.Yes | QMessageBox.No)
                        
                        if reply == QMessageBox.No:
                            return
                    
                    # نسخ الملف إلى المجلد المخصص
                    shutil.copy2(source_file, destination_file)
                    
                    # حفظ مسار الملف في قاعدة البيانات
                    success = self.save_schedule_path_to_db(destination_file)
                    
                    # عرض رسالة نجاح
                    if success:
                        QMessageBox.information(self, "تم بنجاح", 
                            f"تم استيراد ملف جدولة الامتحان بنجاح!\n\n"
                            f"الملف: {original_filename}\n"
                            f"المسار: {destination_file}\n\n"
                            f"✅ تم حفظ الملف في المجلد المخصص\n"
                            f"✅ تم حفظ المسار في قاعدة البيانات")
                    else:
                        QMessageBox.warning(self, "تم الحفظ جزئياً", 
                            f"تم نسخ ملف جدولة الامتحان بنجاح!\n\n"
                            f"الملف: {original_filename}\n"
                            f"المسار: {destination_file}\n\n"
                            f"✅ تم حفظ الملف في المجلد المخصص\n"
                            f"⚠️ لم يتم حفظ المسار في قاعدة البيانات")
                    
                    # تحديث زر عرض الجدولة الحالية
                    self.update_current_schedule_button(original_filename)
                    
                    # فتح الملف للمعاينة
                    reply = QMessageBox.question(self, "معاينة", 
                        "هل تريد فتح الملف للمعاينة؟",
                        QMessageBox.Yes | QMessageBox.No)
                    
                    if reply == QMessageBox.Yes:
                        self.open_pdf_file(destination_file)
                        
        except Exception as e:
            QMessageBox.critical(self, "خطأ", 
                f"حدث خطأ أثناء استيراد ملف جدولة الامتحان:\n{str(e)}")

    def save_schedule_path_to_db(self, file_path):
        """حفظ مسار ملف جدولة الامتحان في قاعدة البيانات"""
        try:
            # الاتصال بقاعدة البيانات
            db_path = "data.db"  # مسار قاعدة البيانات
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # التحقق من وجود جدول بيانات_المؤسسة
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='بيانات_المؤسسة'")
            table_exists = cursor.fetchone()
            
            if table_exists:
                # التحقق من وجود عمود ImagePath2
                cursor.execute("PRAGMA table_info(بيانات_المؤسسة)")
                columns = [column[1] for column in cursor.fetchall()]
                
                # إضافة عمود ImagePath2 إذا لم يكن موجوداً
                if 'ImagePath2' not in columns:
                    cursor.execute("ALTER TABLE بيانات_المؤسسة ADD COLUMN ImagePath2 TEXT")
                    conn.commit()
                
                # التحقق من وجود سجلات في الجدول
                cursor.execute("SELECT COUNT(*) FROM بيانات_المؤسسة")
                record_count = cursor.fetchone()[0]
                
                if record_count > 0:
                    # تحديث أول سجل
                    cursor.execute("""
                        UPDATE بيانات_المؤسسة 
                        SET ImagePath2 = ? 
                        WHERE rowid = (SELECT MIN(rowid) FROM بيانات_المؤسسة)
                    """, (file_path,))
                else:
                    # إدراج سجل جديد
                    cursor.execute("""
                        INSERT INTO بيانات_المؤسسة (ImagePath2) 
                        VALUES (?)
                    """, (file_path,))
            else:
                # إنشاء الجدول إذا لم يكن موجوداً
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS بيانات_المؤسسة (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        ImagePath1 TEXT,
                        ImagePath2 TEXT,
                        institution_name TEXT,
                        address TEXT,
                        phone TEXT,
                        email TEXT
                    )
                """)
                
                # إدراج السجل الأول
                cursor.execute("""
                    INSERT INTO بيانات_المؤسسة (ImagePath2) 
                    VALUES (?)
                """, (file_path,))
            
            conn.commit()
            conn.close()
            
            # التحقق من حفظ المسار بنجاح
            return self.verify_saved_path(file_path)
            
        except Exception as e:
            print(f"خطأ في حفظ المسار في قاعدة البيانات: {str(e)}")
            QMessageBox.critical(self, "خطأ في قاعدة البيانات", 
                f"حدث خطأ في حفظ المسار في قاعدة البيانات:\n{str(e)}\n\n"
                f"تفاصيل الخطأ:\n"
                f"- مسار قاعدة البيانات: data.db\n"
                f"- مسار الملف المراد حفظه: {file_path}\n\n"
                f"سيتم حفظ الملف فقط في المجلد المخصص.")
            return False

    def verify_saved_path(self, expected_path):
        """التحقق من حفظ المسار بنجاح في قاعدة البيانات"""
        try:
            db_path = "data.db"
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            cursor.execute("SELECT ImagePath2 FROM بيانات_المؤسسة WHERE ImagePath2 = ? LIMIT 1", (expected_path,))
            result = cursor.fetchone()
            conn.close()
            
            if result:
                print(f"تم حفظ المسار بنجاح في قاعدة البيانات: {expected_path}")
                return True
            else:
                print(f"فشل في حفظ المسار في قاعدة البيانات: {expected_path}")
                return False
                
        except Exception as e:
            print(f"خطأ في التحقق من حفظ المسار: {str(e)}")
            return False

    def open_pdf_file(self, file_path):
        """فتح ملف PDF للمعاينة"""
        try:
            import sys
            if sys.platform == 'win32':
                os.startfile(file_path)
            elif sys.platform == 'darwin':  # macOS
                subprocess.call(['open', file_path])
            else:  # Linux
                subprocess.call(['xdg-open', file_path])
        except Exception as e:
            QMessageBox.warning(self, "تحذير", 
                f"تعذر فتح الملف للمعاينة:\n{str(e)}")

    def open_reports_folder(self):
        """فتح مجلد جدولة الامتحان في البرنامج"""
        try:
            program_dir = os.path.dirname(os.path.abspath(__file__))
            schedule_folder = os.path.join(program_dir, "جدولة_الامتحان")
            
            # إنشاء المجلد إذا لم يكن موجوداً
            os.makedirs(schedule_folder, exist_ok=True)
            
            # فتح المجلد
            import sys
            if sys.platform == 'win32':
                os.startfile(schedule_folder)
            elif sys.platform == 'darwin':  # macOS
                subprocess.call(['open', schedule_folder])
            else:  # Linux
                subprocess.call(['xdg-open', schedule_folder])
                
            QMessageBox.information(self, "تم بنجاح", 
                f"تم فتح مجلد جدولة الامتحان:\n{schedule_folder}")
                
        except Exception as e:
            QMessageBox.warning(self, "تحذير", 
                f"تعذر فتح المجلد:\n{str(e)}")

    def open_word(self):
        """فتح ملف نموذج Word لاستدعاء التلاميذ"""
        try:
            # تحديد مسار مجلد جدولة_الامتحان داخل مجلد البرنامج
            program_dir = os.path.dirname(os.path.abspath(__file__))
            schedule_folder = os.path.join(program_dir, "جدولة_الامتحان")
            word_template_path = os.path.join(schedule_folder, "نموذج استداء التلاميذ.docx")
            
            # التحقق من وجود الملف
            if os.path.exists(word_template_path):
                # فتح الملف باستخدام البرنامج الافتراضي
                import sys
                if sys.platform == 'win32':
                    os.startfile(word_template_path)
                    QMessageBox.information(self, "تم بنجاح", 
                        f"تم فتح ملف نموذج استدعاء التلاميذ بنجاح!\n\n"
                        f"المسار: {word_template_path}")
                elif sys.platform == 'darwin':  # macOS
                    subprocess.call(['open', word_template_path])
                    QMessageBox.information(self, "تم بنجاح", 
                        f"تم فتح ملف نموذج استدعاء التلاميذ بنجاح!")
                else:  # Linux
                    subprocess.call(['xdg-open', word_template_path])
                    QMessageBox.information(self, "تم بنجاح", 
                        f"تم فتح ملف نموذج استدعاء التلاميذ بنجاح!")
            else:
                # الملف غير موجود - اسأل المستخدم إذا كان يريد إنشاء نموذج أو فتح Word فارغ
                reply = QMessageBox.question(self, "ملف غير موجود", 
                    f"ملف نموذج استدعاء التلاميذ غير موجود في:\n{word_template_path}\n\n"
                    f"اختر ما تريد فعله:\n"
                    f"• Yes: إنشاء نموذج بسيط تلقائياً\n"
                    f"• No: فتح Word فارغ لإنشاء النموذج يدوياً",
                    QMessageBox.Yes | QMessageBox.No | QMessageBox.Cancel)
                
                if reply == QMessageBox.Yes:
                    # إنشاء نموذج Word بسيط
                    self.create_simple_word_template(word_template_path)
                elif reply == QMessageBox.No:
                    # إنشاء المجلد إذا لم يكن موجوداً
                    os.makedirs(schedule_folder, exist_ok=True)
                    
                    # فتح Microsoft Word فارغ
                    if sys.platform == 'win32':
                        try:
                            subprocess.Popen(['winword'])
                            QMessageBox.information(self, "تم بنجاح", 
                                f"تم فتح Microsoft Word بنجاح!\n\n"
                                f"يرجى إنشاء النموذج وحفظه في:\n{word_template_path}")
                        except:
                            os.system('start winword')
                            QMessageBox.information(self, "تم بنجاح", 
                                f"تم فتح Microsoft Word بنجاح!\n\n"
                                f"يرجى إنشاء النموذج وحفظه في:\n{word_template_path}")
                    else:
                        QMessageBox.information(self, "معلومات", 
                            f"يرجى فتح Microsoft Word يدوياً وإنشاء النموذج وحفظه في:\n{word_template_path}")
                        
        except Exception as e:
            QMessageBox.warning(self, "تحذير", 
                f"تعذر فتح ملف نموذج استدعاء التلاميذ:\n{str(e)}\n\n"
                f"تأكد من وجود الملف في:\n"
                f"{os.path.join(os.path.dirname(os.path.abspath(__file__)), 'جدولة_الامتحان', 'نموذج استدعاء التلاميذ.docx')}")
                
        # محاولة فتح Microsoft Word كبديل
        try:
            import sys
            if sys.platform == 'win32':
                subprocess.Popen(['winword'])
                QMessageBox.information(self, "بديل", "تم فتح Microsoft Word فارغ كبديل.")
        except:
            pass

    def show_current_schedule_info(self):
        """عرض معلومات ملف الجدولة الحالي المحفوظ في قاعدة البيانات"""
        try:
            db_path = "data.db"
            if not os.path.exists(db_path):
                QMessageBox.information(self, "لا يوجد ملف", 
                    "لا توجد قاعدة بيانات.\n"
                    "يمكنك استيراد ملف جديد باستخدام زر 'استيراد جدولة الامتحان'.")
                return
                
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # التحقق من وجود جدول بيانات_المؤسسة
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='بيانات_المؤسسة'")
            if not cursor.fetchone():
                conn.close()
                QMessageBox.information(self, "لا يوجد ملف", 
                    "لا يوجد ملف جدولة محفوظ حالياً.\n"
                    "يمكنك استيراد ملف جديد باستخدام زر 'استيراد جدولة الامتحان'.")
                return
            
            # التحقق من وجود عمود ImagePath2
            cursor.execute("PRAGMA table_info(بيانات_المؤسسة)")
            columns = [column[1] for column in cursor.fetchall()]
            
            if 'ImagePath2' not in columns:
                conn.close()
                QMessageBox.information(self, "لا يوجد ملف", 
                    "لا يوجد ملف جدولة محفوظ حالياً.\n"
                    "يمكنك استيراد ملف جديد باستخدام زر 'استيراد جدولة الامتحان'.")
                return
            
            # البحث عن مسار الجدولة المحفوظ
            cursor.execute("SELECT ImagePath2 FROM بيانات_المؤسسة WHERE ImagePath2 IS NOT NULL AND ImagePath2 != '' LIMIT 1")
            result = cursor.fetchone()
            conn.close()
            
            if result and result[0]:
                file_path = result[0]
                if os.path.exists(file_path):
                    filename = os.path.basename(file_path)
                    file_size = os.path.getsize(file_path)
                    file_size_mb = file_size / (1024 * 1024)
                    
                    reply = QMessageBox.question(self, "ملف جدولة موجود", 
                        f"يوجد ملف جدولة محفوظ حالياً:\n\n"
                        f"اسم الملف: {filename}\n"
                        f"حجم الملف: {file_size_mb:.2f} ميجابايت\n"
                        f"المسار: {file_path}\n\n"
                        f"هل تريد فتحه للمعاينة؟",
                        QMessageBox.Yes | QMessageBox.No)
                    
                    if reply == QMessageBox.Yes:
                        self.open_pdf_file(file_path)
                else:
                    # الملف غير موجود، اسأل المستخدم إذا كان يريد حذف المسار من قاعدة البيانات
                    reply = QMessageBox.question(self, "ملف غير موجود", 
                        f"هناك مسار محفوظ في قاعدة البيانات ولكن الملف غير موجود:\n{file_path}\n\n"
                        f"هل تريد حذف هذا المسار من قاعدة البيانات؟",
                        QMessageBox.Yes | QMessageBox.No)
                    
                    if reply == QMessageBox.Yes:
                        self.remove_invalid_path_from_db(file_path)
            else:
                QMessageBox.information(self, "لا يوجد ملف", 
                    "لا يوجد ملف جدولة محفوظ حالياً.\n"
                    "يمكنك استيراد ملف جديد باستخدام زر 'استيراد جدولة الامتحان'.")
                    
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء البحث عن ملف الجدولة:\n{str(e)}")

    def remove_invalid_path_from_db(self, invalid_path):
        """حذف مسار غير صحيح من قاعدة البيانات"""
        try:
            db_path = "data.db"
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            cursor.execute("UPDATE بيانات_المؤسسة SET ImagePath2 = NULL WHERE ImagePath2 = ?", (invalid_path,))
            conn.commit()
            conn.close()
            
            QMessageBox.information(self, "تم الحذف", "تم حذف المسار غير الصحيح من قاعدة البيانات.")
            
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"حدث خطأ أثناء حذف المسار:\n{str(e)}")

    def check_existing_schedule(self):
        """التحقق من وجود جدولة محفوظة عند فتح النافذة"""
        try:
            db_path = "data.db"
            if not os.path.exists(db_path):
                return
                
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # التحقق من وجود جدول بيانات_المؤسسة
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='بيانات_المؤسسة'")
            if not cursor.fetchone():
                conn.close()
                return
            
            # التحقق من وجود عمود ImagePath2
            cursor.execute("PRAGMA table_info(بيانات_المؤسسة)")
            columns = [column[1] for column in cursor.fetchall()]
            
            if 'ImagePath2' not in columns:
                conn.close()
                return
            
            # البحث عن مسار الجدولة المحفوظ
            cursor.execute("SELECT ImagePath2 FROM بيانات_المؤسسة WHERE ImagePath2 IS NOT NULL AND ImagePath2 != '' LIMIT 1")
            result = cursor.fetchone()
            conn.close()
            
            if result and result[0] and os.path.exists(result[0]):
                filename = os.path.basename(result[0])
                # البحث عن زر عرض الجدولة الحالية وتغيير لونه
                self.update_current_schedule_button(filename)
                        
        except Exception as e:
            # في حالة حدوث خطأ، تجاهل التحقق
            pass

    def update_current_schedule_button(self, filename):
        """تحديث زر عرض الجدولة الحالية ليدل على وجود ملف"""
        try:
            # البحث عن الزر وتحديثه
            for i in range(self.layout().count()):
                item = self.layout().itemAt(i)
                if item.layout():
                    for j in range(item.layout().count()):
                        widget_item = item.layout().itemAt(j)
                        if widget_item.widget() and isinstance(widget_item.widget(), QPushButton):
                            if widget_item.widget().text() == "عرض الجدولة الحالية":
                                widget_item.widget().setStyleSheet("""
                                    QPushButton {
                                        background-color: #27ae60;
                                        color: white;
                                        border-radius: 8px;
                                        padding: 10px 20px;
                                        min-height: 35px;
                                        border: 2px solid #2ecc71;
                                    }
                                    QPushButton:hover {
                                        background-color: #229954;
                                    }
                                """)
                                widget_item.widget().setToolTip(f"ملف محفوظ: {filename}")
                                return
        except Exception as e:
            # في حالة حدوث خطأ، تجاهل التحديث
            pass

    def test_database_connection(self):
        """اختبار الاتصال بقاعدة البيانات وعرض معلومات مفيدة"""
        try:
            db_path = "data.db"
            
            # التحقق من وجود ملف قاعدة البيانات
            if not os.path.exists(db_path):
                QMessageBox.warning(self, "قاعدة البيانات", 
                    f"ملف قاعدة البيانات غير موجود:\n{os.path.abspath(db_path)}\n\n"
                    f"سيتم إنشاؤه عند حفظ أول ملف.")
                return
            
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # معلومات عن قاعدة البيانات
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            
            info_text = f"معلومات قاعدة البيانات:\n"
            info_text += f"المسار: {os.path.abspath(db_path)}\n"
            info_text += f"عدد الجداول: {len(tables)}\n\n"
            
            if tables:
                info_text += "الجداول الموجودة:\n"
                for table in tables:
                    info_text += f"- {table[0]}\n"
            
            # التحقق من جدول بيانات_المؤسسة
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='بيانات_المؤسسة'")
            if cursor.fetchone():
                info_text += f"\n✅ جدول بيانات_المؤسسة موجود\n"
                
                # فحص الأعمدة
                cursor.execute("PRAGMA table_info(بيانات_المؤسسة)")
                columns = cursor.fetchall()
                
                info_text += f"الأعمدة الموجودة:\n"
                for col in columns:
                    info_text += f"- {col[1]} ({col[2]})\n"
                
                # التحقق من عمود ImagePath2
                column_names = [col[1] for col in columns]
                if 'ImagePath2' in column_names:
                    info_text += f"✅ عمود ImagePath2 موجود\n"
                    
                    # فحص البيانات
                    cursor.execute("SELECT ImagePath2 FROM بيانات_المؤسسة WHERE ImagePath2 IS NOT NULL")
                    paths = cursor.fetchall()
                    
                    if paths:
                        info_text += f"المسارات المحفوظة: {len(paths)}\n"
                        for path in paths:
                            if os.path.exists(path[0]):
                                info_text += f"✅ {path[0]}\n"
                            else:
                                info_text += f"❌ {path[0]} (غير موجود)\n"
                    else:
                        info_text += f"لا توجد مسارات محفوظة\n"
                else:
                    info_text += f"❌ عمود ImagePath2 غير موجود\n"
            else:
                info_text += f"❌ جدول بيانات_المؤسسة غير موجود\n"
            
            conn.close()
            
            QMessageBox.information(self, "معلومات قاعدة البيانات", info_text)
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء فحص قاعدة البيانات:\n{str(e)}")

    def create_schedule_folder_structure(self):
        """إنشاء هيكل مجلد جدولة_الامتحان مع الملفات الأساسية"""
        try:
            program_dir = os.path.dirname(os.path.abspath(__file__))
            schedule_folder = os.path.join(program_dir, "جدولة_الامتحان")
            
            # إنشاء المجلد
            os.makedirs(schedule_folder, exist_ok=True)
            
            # إنشاء ملف README
            readme_path = os.path.join(schedule_folder, "README.txt")
            if not os.path.exists(readme_path):
                readme_content = """مجلد جدولة الامتحان
==================

هذا المجلد يحتوي على ملفات جدولة الامتحانات والنماذج المتعلقة بها.

الملفات المتوقعة:
- نموذج استدعاء التلاميذ.docx: نموذج Word لاستدعاء الطلاب
- ملفات PDF للجدولة المستوردة
- أي ملفات أخرى متعلقة بالامتحانات

تم إنشاء هذا المجلد تلقائياً بواسطة البرنامج.
"""
                with open(readme_path, 'w', encoding='utf-8') as f:
                    f.write(readme_content)
            
            return schedule_folder
            
        except Exception as e:
            print(f"خطأ في إنشاء هيكل مجلد الجدولة: {e}")
            return None

    def create_simple_word_template(self, file_path):
        """إنشاء نموذج Word بسيط لاستدعاء التلاميذ"""
        try:
            # محاولة استخدام python-docx لإنشاء ملف Word
            try:
                from docx import Document
                from docx.shared import Inches
                from docx.enum.text import WD_ALIGN_PARAGRAPH
                
                # إنشاء مستند جديد
                doc = Document()
                
                # إضافة العنوان الرئيسي
                title = doc.add_heading('استدعاء لحضور الامتحان', level=1)
                title.alignment = WD_ALIGN_PARAGRAPH.CENTER
                
                # إضافة معلومات المؤسسة
                doc.add_paragraph('اسم المؤسسة: _________________________________')
                doc.add_paragraph('العنوان: _____________________________________')
                doc.add_paragraph('')
                
                # إضافة معلومات الامتحان
                doc.add_heading('بيانات الامتحان:', level=2)
                doc.add_paragraph('اسم الامتحان: _______________________________')
                doc.add_paragraph('التاريخ: ____________________________________')
                doc.add_paragraph('الوقت: _____________________________________')
                doc.add_paragraph('القاعة: ____________________________________')
                doc.add_paragraph('')
                
                # إضافة معلومات الطالب
                doc.add_heading('بيانات الطالب:', level=2)
                doc.add_paragraph('الاسم الكامل: _______________________________')
                doc.add_paragraph('رقم الجلوس: ________________________________')
                doc.add_paragraph('الفوج/الشعبة: _______________________________')
                doc.add_paragraph('')
                
                # إضافة التعليمات
                doc.add_heading('تعليمات مهمة:', level=2)
                instructions = [
                    '• الحضور قبل موعد الامتحان بـ 30 دقيقة على الأقل',
                    '• إحضار الهوية الشخصية أو بطاقة الطالب',
                    '• إحضار الأدوات المطلوبة (أقلام، آلة حاسبة، إلخ)',
                    '• عدم إحضار الهاتف المحمول أو أي أجهزة إلكترونية',
                    '• الالتزام بالهدوء واللوائح المنظمة للامتحان'
                ]
                
                for instruction in instructions:
                    doc.add_paragraph(instruction)
                
                doc.add_paragraph('')
                
                # إضافة التوقيع
                doc.add_paragraph('توقيع الإدارة: ____________________________')
                doc.add_paragraph(f'تاريخ الإصدار: {datetime.now().strftime("%Y/%m/%d")}')
                
                # حفظ الملف
                doc.save(file_path)
                
                QMessageBox.information(self, "تم بنجاح", 
                    f"تم إنشاء نموذج استدعاء التلاميذ بنجاح!\n\n"
                    f"المسار: {file_path}\n\n"
                    f"يمكنك الآن فتحه وتخصيصه حسب احتياجاتك.")
                
                # فتح الملف المُنشأ
                self.open_pdf_file(file_path)
                
            except ImportError:
                # إذا لم تكن مكتبة python-docx متاحة، أنشئ ملف نصي بسيط
                self.create_text_template_alternative(file_path)
                
        except Exception as e:
            QMessageBox.warning(self, "خطأ", 
                f"تعذر إنشاء النموذج تلقائياً:\n{str(e)}\n\n"
                f"يرجى إنشاء النموذج يدوياً في Microsoft Word.")

    def create_text_template_alternative(self, file_path):
        """إنشاء ملف نصي كبديل إذا لم تكن مكتبة docx متاحة"""
        text_file_path = file_path.replace('.docx', '_نموذج.txt')
        
        template_content = """استدعاء لحضور الامتحان
====================

اسم المؤسسة: _________________________________
العنوان: _____________________________________

بيانات الامتحان:
-----------------
اسم الامتحان: _______________________________
التاريخ: ____________________________________
الوقت: _____________________________________
القاعة: ____________________________________

بيانات الطالب:
---------------
الاسم الكامل: _______________________________
رقم الجلوس: ________________________________
الفوج/الشعبة: _______________________________

تعليمات مهمة:
--------------
• الحضور قبل موعد الامتحان بـ 30 دقيقة على الأقل
• إحضار الهوية الشخصية أو بطاقة الطالب
• إحضار الأدوات المطلوبة (أقلام، آلة حاسبة، إلخ)
• عدم إحضار الهاتف المحمول أو أي أجهزة إلكترونية
• الالتزام بالهدوء واللوائح المنظمة للامتحان

توقيع الإدارة: ____________________________
تاريخ الإصدار: """ + datetime.now().strftime("%Y/%m/%d") + """

ملاحظة: هذا ملف نصي. يرجى نسخ المحتوى إلى Microsoft Word وتنسيقه."""
        
        with open(text_file_path, 'w', encoding='utf-8') as f:
            f.write(template_content)
        
        QMessageBox.information(self, "تم إنشاء نموذج نصي", 
            f"تم إنشاء نموذج نصي بسيط:\n{text_file_path}\n\n"
            f"يرجى نسخ المحتوى إلى Microsoft Word وحفظه باسم:\n{file_path}")
        
        # فتح الملف النصي
        self.open_pdf_file(text_file_path)

# الكلاس القديم للتوافق مع الكود الحالي
class ExamScheduleProfessional(ExamScheduleInstructions):
    """
    كلاس للتوافق مع الكود الحالي
    يعيد توجيه إلى نافذة التعليمات الجديدة
    """
    def __init__(self, parent=None, db_path="data.db"):
        super().__init__(parent)


# تشغيل التطبيق كنافذة مستقلة للاختبار
if __name__ == "__main__":
    import sys
    from PyQt5.QtWidgets import QApplication

    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    dialog = ExamScheduleInstructions()
    dialog.show()
    sys.exit(app.exec_())
