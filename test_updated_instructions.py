#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التعليمات المحدثة في نافذة جدولة الامتحان
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt

# إضافة مسار المجلد الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    """اختبار النافذة مع التعليمات المحدثة"""
    try:
        from sub26_window import ExamScheduleInstructions
        
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء النافذة
        window = ExamScheduleInstructions()
        window.show()
        
        print("🚀 تم فتح نافذة التعليمات المحدثة!")
        print("\n📋 التعليمات الجديدة المضافة:")
        print("✅ شرح استخدام النموذج المدرج في مجلد البرنامج")
        print("✅ طريقة حفظ النموذج كملف PDF باستخدام طابعة PDF")
        print("✅ خطوات الطباعة عبر Microsoft Print to PDF")
        print("✅ مميزات الطباعة عبر PDF")
        print("✅ نصائح إضافية حول النماذج الجاهزة والطباعة المتقدمة")
        
        print("\n📍 الأقسام المحدثة:")
        print("- إنشاء نموذج استدعاء التلاميذ")
        print("- حفظ الملف كـ PDF")
        print("- طباعة النماذج والجدولة (قسم جديد)")
        print("- نصائح مهمة")
        
        print("\n💡 للاختبار:")
        print("- تصفح التعليمات في النافذة")
        print("- جرب زر 'فتح نموذج استدعاء التلاميذ'")
        print("- تحقق من الأقسام الجديدة المضافة")
        
        # تشغيل التطبيق
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل النافذة: {e}")

if __name__ == "__main__":
    main()
